[{"D:\\invoice_project\\gst-billing-software\\frontend\\src\\index.js": "1", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\reportWebVitals.js": "2", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\App.js": "3", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\context\\AuthContext.js": "4", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\context\\ToastContext.js": "5", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\Navbar.js": "6", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\PrivateRoute.js": "7", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\DashboardLayout.js": "8", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\NewLandingPage.js": "9", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\invoices\\Invoices.js": "10", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\invoices\\EditInvoice.js": "11", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\customers\\Customers.js": "12", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\invoices\\CreateInvoice.js": "13", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\auth\\Login.js": "14", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\auth\\Register.js": "15", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\reports\\Reports.js": "16", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\products\\Products.js": "17", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\dashboard\\Dashboard.js": "18", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\ToastContainer.jsx": "19", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\config.js": "20", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\Sidebar.js": "21", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\LandingNavbar.js": "22", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\BusinessTools.jsx": "23", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\AIChatbot.jsx": "24", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\BestSellersChart.jsx": "25", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\SalesChart.jsx": "26", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\GSTNews.jsx": "27", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\QuickAccess.jsx": "28", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\ProfileManagement.jsx": "29", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\InvoiceTemplate.jsx": "30", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\Tutorials.js": "31", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\ContactUs.js": "32", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\PrivacyPolicy.js": "33", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\HelpCenter.js": "34", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\TermsOfService.js": "35", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\dashboard\\Subscription.js": "36", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\SubscriptionStatus.jsx": "37", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\SubscriptionManager.jsx": "38", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\TopSubscriptionStatus.jsx": "39", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\hooks\\useSubscriptionLimits.js": "40", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\SubscriptionUpgradeModal.jsx": "41", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\ConfirmationModal.jsx": "42", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\ProductBranding.jsx": "43", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\admin\\AdminDashboard.js": "44", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\admin\\AdminLogin.js": "45", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\CustomerManagement.jsx": "46", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\AdminOverview.jsx": "47", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\PaymentManagement.jsx": "48", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\Analytics.jsx": "49", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\SystemSettings.jsx": "50", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\AdminProfileModal.jsx": "51", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\NotificationPanel.jsx": "52", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\NotificationManager.jsx": "53", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\NotificationsPage.jsx": "54", "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\SubscriptionStatus.jsx": "55"}, {"size": 369, "mtime": 1743077628947, "results": "56", "hashOfConfig": "57"}, {"size": 362, "mtime": 1742904806515, "results": "58", "hashOfConfig": "57"}, {"size": 4980, "mtime": 1748435317413, "results": "59", "hashOfConfig": "57"}, {"size": 5386, "mtime": 1749329808678, "results": "60", "hashOfConfig": "57"}, {"size": 2132, "mtime": 1748012854907, "results": "61", "hashOfConfig": "57"}, {"size": 2402, "mtime": 1745866734325, "results": "62", "hashOfConfig": "57"}, {"size": 281, "mtime": 1743077790146, "results": "63", "hashOfConfig": "57"}, {"size": 765, "mtime": 1745757715610, "results": "64", "hashOfConfig": "57"}, {"size": 29379, "mtime": 1748425111989, "results": "65", "hashOfConfig": "57"}, {"size": 19297, "mtime": 1748349162024, "results": "66", "hashOfConfig": "57"}, {"size": 58397, "mtime": 1748325643669, "results": "67", "hashOfConfig": "57"}, {"size": 26389, "mtime": 1748010380127, "results": "68", "hashOfConfig": "57"}, {"size": 61175, "mtime": 1749330006337, "results": "69", "hashOfConfig": "57"}, {"size": 4482, "mtime": 1748424372161, "results": "70", "hashOfConfig": "57"}, {"size": 9426, "mtime": 1749329794491, "results": "71", "hashOfConfig": "57"}, {"size": 27555, "mtime": 1748328725451, "results": "72", "hashOfConfig": "57"}, {"size": 23290, "mtime": 1748010224220, "results": "73", "hashOfConfig": "57"}, {"size": 16703, "mtime": 1748719613596, "results": "74", "hashOfConfig": "57"}, {"size": 2106, "mtime": 1748012881622, "results": "75", "hashOfConfig": "57"}, {"size": 85, "mtime": 1743077666118, "results": "76", "hashOfConfig": "57"}, {"size": 10938, "mtime": 1749329920243, "results": "77", "hashOfConfig": "57"}, {"size": 2871, "mtime": 1748425093943, "results": "78", "hashOfConfig": "57"}, {"size": 57142, "mtime": 1748349382316, "results": "79", "hashOfConfig": "57"}, {"size": 4487, "mtime": 1748702743458, "results": "80", "hashOfConfig": "57"}, {"size": 9453, "mtime": 1748095149860, "results": "81", "hashOfConfig": "57"}, {"size": 7416, "mtime": 1748095130408, "results": "82", "hashOfConfig": "57"}, {"size": 2819, "mtime": 1745376089515, "results": "83", "hashOfConfig": "57"}, {"size": 19380, "mtime": 1745854865981, "results": "84", "hashOfConfig": "57"}, {"size": 23954, "mtime": 1749329885625, "results": "85", "hashOfConfig": "57"}, {"size": 23796, "mtime": 1748327902156, "results": "86", "hashOfConfig": "57"}, {"size": 10904, "mtime": 1748257659151, "results": "87", "hashOfConfig": "57"}, {"size": 8457, "mtime": 1748259959283, "results": "88", "hashOfConfig": "57"}, {"size": 9219, "mtime": 1748258827206, "results": "89", "hashOfConfig": "57"}, {"size": 9163, "mtime": 1748257584814, "results": "90", "hashOfConfig": "57"}, {"size": 10764, "mtime": 1748258748443, "results": "91", "hashOfConfig": "57"}, {"size": 8007, "mtime": 1748741157939, "results": "92", "hashOfConfig": "57"}, {"size": 6586, "mtime": 1748262725063, "results": "93", "hashOfConfig": "57"}, {"size": 10926, "mtime": 1748740855950, "results": "94", "hashOfConfig": "57"}, {"size": 4436, "mtime": 1748740264941, "results": "95", "hashOfConfig": "57"}, {"size": 3184, "mtime": 1748349397387, "results": "96", "hashOfConfig": "57"}, {"size": 5157, "mtime": 1748348989710, "results": "97", "hashOfConfig": "57"}, {"size": 2448, "mtime": 1748350033978, "results": "98", "hashOfConfig": "57"}, {"size": 1551, "mtime": 1748424291543, "results": "99", "hashOfConfig": "57"}, {"size": 9857, "mtime": 1748725186654, "results": "100", "hashOfConfig": "57"}, {"size": 8099, "mtime": 1748509737822, "results": "101", "hashOfConfig": "57"}, {"size": 14423, "mtime": 1748738903486, "results": "102", "hashOfConfig": "57"}, {"size": 19453, "mtime": 1748729802829, "results": "103", "hashOfConfig": "57"}, {"size": 13471, "mtime": 1748728529558, "results": "104", "hashOfConfig": "57"}, {"size": 14400, "mtime": 1748728588163, "results": "105", "hashOfConfig": "57"}, {"size": 12139, "mtime": 1748728667382, "results": "106", "hashOfConfig": "57"}, {"size": 8051, "mtime": 1748503408096, "results": "107", "hashOfConfig": "57"}, {"size": 2560, "mtime": 1748719514097, "results": "108", "hashOfConfig": "57"}, {"size": 15963, "mtime": 1748733675328, "results": "109", "hashOfConfig": "57"}, {"size": 9781, "mtime": 1748720913252, "results": "110", "hashOfConfig": "57"}, {"size": 40563, "mtime": 1748737510686, "results": "111", "hashOfConfig": "57"}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1sohedj", {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\invoice_project\\gst-billing-software\\frontend\\src\\index.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\reportWebVitals.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\App.js", ["277", "278"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\context\\AuthContext.js", ["279"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\context\\ToastContext.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\Navbar.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\PrivateRoute.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\DashboardLayout.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\NewLandingPage.js", ["280", "281", "282", "283", "284", "285"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\invoices\\Invoices.js", ["286", "287", "288"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\invoices\\EditInvoice.js", ["289"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\customers\\Customers.js", ["290", "291", "292"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\invoices\\CreateInvoice.js", ["293", "294"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\auth\\Login.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\auth\\Register.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\reports\\Reports.js", ["295", "296", "297", "298", "299"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\products\\Products.js", ["300", "301", "302"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\dashboard\\Dashboard.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\ToastContainer.jsx", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\config.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\Sidebar.js", ["303", "304"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\LandingNavbar.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\BusinessTools.jsx", ["305"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\AIChatbot.jsx", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\BestSellersChart.jsx", ["306", "307", "308", "309", "310", "311"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\SalesChart.jsx", ["312", "313", "314", "315", "316"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\GSTNews.jsx", ["317"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\QuickAccess.jsx", ["318", "319", "320", "321"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\ProfileManagement.jsx", ["322", "323"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\InvoiceTemplate.jsx", ["324", "325", "326", "327", "328", "329"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\Tutorials.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\ContactUs.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\HelpCenter.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\TermsOfService.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\dashboard\\Subscription.js", ["330", "331", "332", "333", "334"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\SubscriptionStatus.jsx", ["335"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\SubscriptionManager.jsx", ["336"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\TopSubscriptionStatus.jsx", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\hooks\\useSubscriptionLimits.js", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\SubscriptionUpgradeModal.jsx", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\ConfirmationModal.jsx", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\common\\ProductBranding.jsx", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\admin\\AdminDashboard.js", ["337", "338", "339"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\pages\\admin\\AdminLogin.js", ["340"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\CustomerManagement.jsx", ["341", "342"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\AdminOverview.jsx", ["343", "344"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\PaymentManagement.jsx", ["345"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\Analytics.jsx", ["346", "347"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\SystemSettings.jsx", ["348", "349"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\AdminProfileModal.jsx", [], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\NotificationPanel.jsx", ["350", "351"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\NotificationManager.jsx", ["352", "353", "354", "355"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\dashboard\\NotificationsPage.jsx", ["356", "357", "358", "359", "360"], [], "D:\\invoice_project\\gst-billing-software\\frontend\\src\\components\\admin\\SubscriptionStatus.jsx", ["361", "362"], [], {"ruleId": "363", "severity": 1, "message": "364", "line": 3, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 3, "endColumn": 19}, {"ruleId": "363", "severity": 1, "message": "367", "line": 7, "column": 8, "nodeType": "365", "messageId": "366", "endLine": 7, "endColumn": 14}, {"ruleId": "368", "severity": 1, "message": "369", "line": 66, "column": 6, "nodeType": "370", "endLine": 66, "endColumn": 8, "suggestions": "371"}, {"ruleId": "363", "severity": 1, "message": "372", "line": 5, "column": 59, "nodeType": "365", "messageId": "366", "endLine": 5, "endColumn": 70}, {"ruleId": "363", "severity": 1, "message": "373", "line": 19, "column": 8, "nodeType": "365", "messageId": "366", "endLine": 19, "endColumn": 19}, {"ruleId": "374", "severity": 1, "message": "375", "line": 665, "column": 17, "nodeType": "376", "endLine": 665, "endColumn": 53}, {"ruleId": "374", "severity": 1, "message": "375", "line": 666, "column": 17, "nodeType": "376", "endLine": 666, "endColumn": 53}, {"ruleId": "374", "severity": 1, "message": "375", "line": 667, "column": 17, "nodeType": "376", "endLine": 667, "endColumn": 53}, {"ruleId": "374", "severity": 1, "message": "375", "line": 668, "column": 17, "nodeType": "376", "endLine": 668, "endColumn": 53}, {"ruleId": "363", "severity": 1, "message": "377", "line": 2, "column": 38, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 49}, {"ruleId": "363", "severity": 1, "message": "378", "line": 6, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 6, "endColumn": 14}, {"ruleId": "368", "severity": 1, "message": "379", "line": 51, "column": 6, "nodeType": "370", "endLine": 51, "endColumn": 19, "suggestions": "380"}, {"ruleId": "368", "severity": 1, "message": "381", "line": 64, "column": 6, "nodeType": "370", "endLine": 64, "endColumn": 23, "suggestions": "382"}, {"ruleId": "363", "severity": 1, "message": "383", "line": 2, "column": 44, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 51}, {"ruleId": "363", "severity": 1, "message": "384", "line": 68, "column": 11, "nodeType": "365", "messageId": "366", "endLine": 68, "endColumn": 22}, {"ruleId": "363", "severity": 1, "message": "385", "line": 69, "column": 35, "nodeType": "365", "messageId": "366", "endLine": 69, "endColumn": 46}, {"ruleId": "363", "severity": 1, "message": "385", "line": 60, "column": 35, "nodeType": "365", "messageId": "366", "endLine": 60, "endColumn": 46}, {"ruleId": "368", "severity": 1, "message": "386", "line": 71, "column": 6, "nodeType": "370", "endLine": 71, "endColumn": 19, "suggestions": "387"}, {"ruleId": "363", "severity": 1, "message": "388", "line": 2, "column": 52, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 57}, {"ruleId": "363", "severity": 1, "message": "385", "line": 31, "column": 35, "nodeType": "365", "messageId": "366", "endLine": 31, "endColumn": 46}, {"ruleId": "363", "severity": 1, "message": "389", "line": 33, "column": 9, "nodeType": "365", "messageId": "366", "endLine": 33, "endColumn": 21}, {"ruleId": "363", "severity": 1, "message": "390", "line": 133, "column": 9, "nodeType": "365", "messageId": "366", "endLine": 133, "endColumn": 24}, {"ruleId": "368", "severity": 1, "message": "391", "line": 360, "column": 6, "nodeType": "370", "endLine": 360, "endColumn": 19, "suggestions": "392"}, {"ruleId": "363", "severity": 1, "message": "385", "line": 27, "column": 35, "nodeType": "365", "messageId": "366", "endLine": 27, "endColumn": 46}, {"ruleId": "368", "severity": 1, "message": "393", "line": 40, "column": 6, "nodeType": "370", "endLine": 40, "endColumn": 19, "suggestions": "394"}, {"ruleId": "368", "severity": 1, "message": "395", "line": 48, "column": 6, "nodeType": "370", "endLine": 48, "endColumn": 8, "suggestions": "396"}, {"ruleId": "363", "severity": 1, "message": "397", "line": 19, "column": 3, "nodeType": "365", "messageId": "366", "endLine": 19, "endColumn": 9}, {"ruleId": "368", "severity": 1, "message": "398", "line": 52, "column": 6, "nodeType": "370", "endLine": 52, "endColumn": 19, "suggestions": "399"}, {"ruleId": "363", "severity": 1, "message": "400", "line": 48, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 48, "endColumn": 23}, {"ruleId": "363", "severity": 1, "message": "401", "line": 2, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 14}, {"ruleId": "363", "severity": 1, "message": "402", "line": 2, "column": 22, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 25}, {"ruleId": "363", "severity": 1, "message": "403", "line": 2, "column": 27, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 30}, {"ruleId": "363", "severity": 1, "message": "404", "line": 2, "column": 32, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 35}, {"ruleId": "363", "severity": 1, "message": "405", "line": 4, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 4, "endColumn": 18}, {"ruleId": "368", "severity": 1, "message": "406", "line": 35, "column": 6, "nodeType": "370", "endLine": 35, "endColumn": 28, "suggestions": "407"}, {"ruleId": "363", "severity": 1, "message": "401", "line": 2, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 14}, {"ruleId": "363", "severity": 1, "message": "402", "line": 2, "column": 22, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 25}, {"ruleId": "363", "severity": 1, "message": "403", "line": 2, "column": 27, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 30}, {"ruleId": "363", "severity": 1, "message": "405", "line": 4, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 4, "endColumn": 18}, {"ruleId": "368", "severity": 1, "message": "408", "line": 33, "column": 6, "nodeType": "370", "endLine": 33, "endColumn": 17, "suggestions": "409"}, {"ruleId": "363", "severity": 1, "message": "401", "line": 2, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 14}, {"ruleId": "363", "severity": 1, "message": "401", "line": 2, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 14}, {"ruleId": "368", "severity": 1, "message": "410", "line": 237, "column": 6, "nodeType": "370", "endLine": 237, "endColumn": 22, "suggestions": "411"}, {"ruleId": "363", "severity": 1, "message": "412", "line": 319, "column": 9, "nodeType": "365", "messageId": "366", "endLine": 319, "endColumn": 30}, {"ruleId": "374", "severity": 1, "message": "375", "line": 343, "column": 13, "nodeType": "376", "endLine": 348, "endColumn": 14}, {"ruleId": "363", "severity": 1, "message": "385", "line": 37, "column": 35, "nodeType": "365", "messageId": "366", "endLine": 37, "endColumn": 46}, {"ruleId": "368", "severity": 1, "message": "398", "line": 43, "column": 6, "nodeType": "370", "endLine": 43, "endColumn": 19, "suggestions": "413"}, {"ruleId": "363", "severity": 1, "message": "414", "line": 2, "column": 40, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 45}, {"ruleId": "363", "severity": 1, "message": "415", "line": 2, "column": 54, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 59}, {"ruleId": "363", "severity": 1, "message": "416", "line": 12, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 12, "endColumn": 26}, {"ruleId": "368", "severity": 1, "message": "417", "line": 75, "column": 6, "nodeType": "370", "endLine": 75, "endColumn": 19, "suggestions": "418"}, {"ruleId": "363", "severity": 1, "message": "419", "line": 132, "column": 13, "nodeType": "365", "messageId": "366", "endLine": 132, "endColumn": 21}, {"ruleId": "363", "severity": 1, "message": "419", "line": 151, "column": 13, "nodeType": "365", "messageId": "366", "endLine": 151, "endColumn": 21}, {"ruleId": "363", "severity": 1, "message": "364", "line": 2, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 19}, {"ruleId": "363", "severity": 1, "message": "420", "line": 3, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 3, "endColumn": 17}, {"ruleId": "363", "severity": 1, "message": "421", "line": 3, "column": 44, "nodeType": "365", "messageId": "366", "endLine": 3, "endColumn": 55}, {"ruleId": "363", "severity": 1, "message": "422", "line": 16, "column": 9, "nodeType": "365", "messageId": "366", "endLine": 16, "endColumn": 17}, {"ruleId": "368", "severity": 1, "message": "423", "line": 24, "column": 6, "nodeType": "370", "endLine": 24, "endColumn": 17, "suggestions": "424"}, {"ruleId": "368", "severity": 1, "message": "425", "line": 15, "column": 6, "nodeType": "370", "endLine": 15, "endColumn": 8, "suggestions": "426"}, {"ruleId": "368", "severity": 1, "message": "425", "line": 21, "column": 6, "nodeType": "370", "endLine": 21, "endColumn": 8, "suggestions": "427"}, {"ruleId": "363", "severity": 1, "message": "401", "line": 2, "column": 31, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 35}, {"ruleId": "368", "severity": 1, "message": "428", "line": 34, "column": 6, "nodeType": "370", "endLine": 34, "endColumn": 8, "suggestions": "429"}, {"ruleId": "368", "severity": 1, "message": "430", "line": 40, "column": 6, "nodeType": "370", "endLine": 40, "endColumn": 15, "suggestions": "431"}, {"ruleId": "368", "severity": 1, "message": "432", "line": 30, "column": 6, "nodeType": "370", "endLine": 30, "endColumn": 8, "suggestions": "433"}, {"ruleId": "363", "severity": 1, "message": "434", "line": 22, "column": 3, "nodeType": "365", "messageId": "366", "endLine": 22, "endColumn": 10}, {"ruleId": "368", "severity": 1, "message": "435", "line": 47, "column": 6, "nodeType": "370", "endLine": 47, "endColumn": 57, "suggestions": "436"}, {"ruleId": "363", "severity": 1, "message": "437", "line": 12, "column": 3, "nodeType": "365", "messageId": "366", "endLine": 12, "endColumn": 14}, {"ruleId": "363", "severity": 1, "message": "438", "line": 17, "column": 3, "nodeType": "365", "messageId": "366", "endLine": 17, "endColumn": 14}, {"ruleId": "368", "severity": 1, "message": "439", "line": 43, "column": 6, "nodeType": "370", "endLine": 43, "endColumn": 47, "suggestions": "440"}, {"ruleId": "363", "severity": 1, "message": "441", "line": 6, "column": 3, "nodeType": "365", "messageId": "366", "endLine": 6, "endColumn": 16}, {"ruleId": "368", "severity": 1, "message": "442", "line": 26, "column": 6, "nodeType": "370", "endLine": 26, "endColumn": 8, "suggestions": "443"}, {"ruleId": "363", "severity": 1, "message": "444", "line": 8, "column": 3, "nodeType": "365", "messageId": "366", "endLine": 8, "endColumn": 7}, {"ruleId": "363", "severity": 1, "message": "445", "line": 20, "column": 3, "nodeType": "365", "messageId": "366", "endLine": 20, "endColumn": 10}, {"ruleId": "363", "severity": 1, "message": "446", "line": 2, "column": 17, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 23}, {"ruleId": "368", "severity": 1, "message": "447", "line": 17, "column": 6, "nodeType": "370", "endLine": 17, "endColumn": 19, "suggestions": "448"}, {"ruleId": "363", "severity": 1, "message": "364", "line": 2, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "449", "line": 24, "column": 6, "nodeType": "370", "endLine": 24, "endColumn": 8, "suggestions": "450"}, {"ruleId": "451", "severity": 1, "message": "452", "line": 394, "column": 76, "nodeType": "453", "messageId": "454", "endLine": 394, "endColumn": 78}, {"ruleId": "451", "severity": 1, "message": "452", "line": 400, "column": 45, "nodeType": "453", "messageId": "454", "endLine": 400, "endColumn": 47}, {"ruleId": "363", "severity": 1, "message": "402", "line": 2, "column": 56, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 59}, {"ruleId": "363", "severity": 1, "message": "403", "line": 2, "column": 61, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 64}, {"ruleId": "363", "severity": 1, "message": "455", "line": 3, "column": 27, "nodeType": "365", "messageId": "366", "endLine": 3, "endColumn": 32}, {"ruleId": "363", "severity": 1, "message": "445", "line": 3, "column": 34, "nodeType": "365", "messageId": "366", "endLine": 3, "endColumn": 41}, {"ruleId": "368", "severity": 1, "message": "456", "line": 21, "column": 6, "nodeType": "370", "endLine": 21, "endColumn": 19, "suggestions": "457"}, {"ruleId": "363", "severity": 1, "message": "364", "line": 2, "column": 10, "nodeType": "365", "messageId": "366", "endLine": 2, "endColumn": 19}, {"ruleId": "368", "severity": 1, "message": "425", "line": 39, "column": 6, "nodeType": "370", "endLine": 39, "endColumn": 8, "suggestions": "458"}, "no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "'Navbar' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'logout'. Either include it or remove the dependency array.", "ArrayExpression", ["459"], "'FaMobileAlt' is defined but never used.", "'companyLogo' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'ButtonGroup' is defined but never used.", "'Link' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchDefaultTemplate', 'fetchInvoices', 'invoices.length', 'loading', and 'previewData.url'. Either include them or remove the dependency array.", ["460"], "React Hook useEffect has a missing dependency: 'fetchInvoiceData'. Either include it or remove the dependency array.", ["461"], "'Spinner' is defined but never used.", "'currentUser' is assigned a value but never used.", "'showWarning' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'generateInvoiceNumber'. Either include it or remove the dependency array.", ["462"], "'Badge' is defined but never used.", "'fetchReports' is assigned a value but never used.", "'fetchHsnSummary' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllReports'. Either include it or remove the dependency array.", ["463"], "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["464"], "React Hook useEffect has missing dependencies: 'currentUser?.token', 'fetchProducts', 'isLoading', and 'products.length'. Either include them or remove the dependency array.", ["465"], "'faUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", ["466"], "'exchangeRates' is assigned a value but never used.", "'Card' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "'Nav' is defined but never used.", "'useToast' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["467"], "React Hook useEffect has a missing dependency: 'fetchSalesData'. Either include it or remove the dependency array.", ["468"], "React Hook useEffect has a missing dependency: 'fetchRecentInvoices'. Either include it or remove the dependency array.", ["469"], "'refreshRecentInvoices' is assigned a value but never used.", ["470"], "'Alert' is defined but never used.", "'Table' is defined but never used.", "'selectedTemplate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTemplates'. Either include it or remove the dependency array.", ["471"], "'response' is assigned a value but never used.", "'FaCrown' is defined but never used.", "'FaArrowLeft' is defined but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchPaymentHistory' and 'fetchSubscriptionHistory'. Either include them or remove the dependency array.", ["472"], "React Hook useEffect has a missing dependency: 'fetchSubscriptionData'. Either include it or remove the dependency array.", ["473"], ["474"], "React Hook useEffect has a missing dependency: 'checkAdminAccess'. Either include it or remove the dependency array.", ["475"], "React Hook useEffect has a missing dependency: 'fetchAdminStats'. Either include it or remove the dependency array.", ["476"], "React Hook useEffect has a missing dependency: 'verifyAdminToken'. Either include it or remove the dependency array.", ["477"], "'FaPhone' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchCustomers'. Either include it or remove the dependency array.", ["478"], "'FaArrowDown' is defined but never used.", "'FaChartLine' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", ["479"], "'FaFileInvoice' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchAnalytics' and 'fetchRevenueAnalytics'. Either include them or remove the dependency array.", ["480"], "'Form' is defined but never used.", "'FaTrash' is defined but never used.", "'Button' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchNotificationCount'. Either include it or remove the dependency array.", ["481"], "React Hook useEffect has missing dependencies: 'fetchTemplates' and 'fetchUsers'. Either include them or remove the dependency array.", ["482"], "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'FaEye' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchNotificationCount' and 'fetchNotifications'. Either include them or remove the dependency array.", ["483"], ["484"], {"desc": "485", "fix": "486"}, {"desc": "487", "fix": "488"}, {"desc": "489", "fix": "490"}, {"desc": "491", "fix": "492"}, {"desc": "493", "fix": "494"}, {"desc": "495", "fix": "496"}, {"desc": "497", "fix": "498"}, {"desc": "499", "fix": "500"}, {"desc": "501", "fix": "502"}, {"desc": "503", "fix": "504"}, {"desc": "505", "fix": "506"}, {"desc": "499", "fix": "507"}, {"desc": "508", "fix": "509"}, {"desc": "510", "fix": "511"}, {"desc": "512", "fix": "513"}, {"desc": "512", "fix": "514"}, {"desc": "515", "fix": "516"}, {"desc": "517", "fix": "518"}, {"desc": "519", "fix": "520"}, {"desc": "521", "fix": "522"}, {"desc": "523", "fix": "524"}, {"desc": "525", "fix": "526"}, {"desc": "527", "fix": "528"}, {"desc": "529", "fix": "530"}, {"desc": "531", "fix": "532"}, {"desc": "512", "fix": "533"}, "Update the dependencies array to be: [logout]", {"range": "534", "text": "535"}, "Update the dependencies array to be: [currentUser, fetchDefaultTemplate, fetchInvoices, invoices.length, loading, previewData.url]", {"range": "536", "text": "537"}, "Update the dependencies array to be: [currentUser, fetchInvoiceData, id]", {"range": "538", "text": "539"}, "Update the dependencies array to be: [currentUser, generateInvoiceNumber]", {"range": "540", "text": "541"}, "Update the dependencies array to be: [currentUser, fetchAllReports]", {"range": "542", "text": "543"}, "Update the dependencies array to be: [currentUser, fetchProducts]", {"range": "544", "text": "545"}, "Update the dependencies array to be: [currentUser?.token, fetchProducts, isLoading, products.length]", {"range": "546", "text": "547"}, "Update the dependencies array to be: [currentUser, fetchUserProfile]", {"range": "548", "text": "549"}, "Update the dependencies array to be: [activeTab, fetchData, timeRange]", {"range": "550", "text": "551"}, "Update the dependencies array to be: [chartType, fetchSalesData]", {"range": "552", "text": "553"}, "Update the dependencies array to be: [fetchRecentInvoices, refreshTrigger]", {"range": "554", "text": "555"}, {"range": "556", "text": "549"}, "Update the dependencies array to be: [currentUser, fetchTemplates]", {"range": "557", "text": "558"}, "Update the dependencies array to be: [activeTab, fetchPaymentHistory, fetchSubscriptionHistory]", {"range": "559", "text": "560"}, "Update the dependencies array to be: [fetchSubscriptionData]", {"range": "561", "text": "562"}, {"range": "563", "text": "562"}, "Update the dependencies array to be: [checkAdminAccess]", {"range": "564", "text": "565"}, "Update the dependencies array to be: [fetchAdminStats, isAdmin]", {"range": "566", "text": "567"}, "Update the dependencies array to be: [verifyAdminToken]", {"range": "568", "text": "569"}, "Update the dependencies array to be: [currentPage, searchTerm, planFilter, statusFilter, fetchCustomers]", {"range": "570", "text": "571"}, "Update the dependencies array to be: [currentPage, statusFilter, methodFilter, fetchPayments]", {"range": "572", "text": "573"}, "Update the dependencies array to be: [fetchAnalytics, fetchRevenueAnalytics]", {"range": "574", "text": "575"}, "Update the dependencies array to be: [currentUser, fetchNotificationCount]", {"range": "576", "text": "577"}, "Update the dependencies array to be: [fetchTemplates, fetchUsers]", {"range": "578", "text": "579"}, "Update the dependencies array to be: [currentUser, fetchNotificationCount, fetchNotifications]", {"range": "580", "text": "581"}, {"range": "582", "text": "562"}, [2226, 2228], "[logout]", [2229, 2242], "[currentUser, fetchDefaultTemplate, fetchInvoices, invoices.length, loading, previewData.url]", [1897, 1914], "[currentUser, fetchInvoiceData, id]", [2448, 2461], "[currentUser, generateInvoiceNumber]", [12639, 12652], "[current<PERSON><PERSON>, fetchAllReports]", [1666, 1679], "[currentUser, fetchProducts]", [1976, 1978], "[currentUser?.token, fetchProducts, isLoading, products.length]", [1678, 1691], "[currentUser, fetchUserProfile]", [1331, 1353], "[activeTab, fetchData, timeRange]", [1278, 1289], "[chartType, fetchSalesData]", [8949, 8965], "[fetchRecentInvoices, refreshTrigger]", [1255, 1268], [2452, 2465], "[currentUser, fetchTemplates]", [988, 999], "[activeTab, fetchPaymentHistory, fetchSubscriptionHistory]", [602, 604], "[fetchSubscriptionData]", [826, 828], [1594, 1596], "[checkAdminAccess]", [1675, 1684], "[fetchAdminStats, isAdmin]", [1017, 1019], "[verifyAdminToken]", [1147, 1198], "[currentPage, searchTerm, planFilter, statusFilter, fetchCustomers]", [1077, 1118], "[currentPage, statusFilter, methodFilter, fetchPayments]", [768, 770], "[fetchAnalytics, fetchRevenueAnalytics]", [523, 536], "[currentUser, fetchNotificationCount]", [1003, 1005], "[fetchTemplates, fetchUsers]", [846, 859], "[currentUser, fetchNotificationCount, fetchNotifications]", [1270, 1272]]