import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import ProductBranding from './common/ProductBranding';
import axios from 'axios';
import '../styles/sidebar.css';

// Import icons if FontAwesome is available
let FontAwesomeIcon;
let faTachometerAlt, faUsers, faBoxes, faFileInvoice, faChartBar, faUser, faSignOutAlt, faCog, faTools, faFileAlt, faCrown, faBell;
try {
  FontAwesomeIcon = require('@fortawesome/react-fontawesome').FontAwesomeIcon;
  const icons = require('@fortawesome/free-solid-svg-icons');
  faTachometerAlt = icons.faTachometerAlt;
  faUsers = icons.faUsers;
  faBoxes = icons.faBoxes;
  faFileInvoice = icons.faFileInvoice;
  faChartBar = icons.faChartBar;
  faUser = icons.faUser;
  faSignOutAlt = icons.faSignOutAlt;
  faCog = icons.faCog;
  faTools = icons.faTools;
  faFileAlt = icons.faFileAlt;
  faCrown = icons.faCrown;
  faBell = icons.faBell;
} catch (error) {
  console.error('Error loading FontAwesome:', error);
}

export default function Sidebar({ onLogout }) {
  const { currentUser } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [userData, setUserData] = useState({
    company_name: '',
    logo_url: '',
    profile_completion_percentage: 0,
    profile_completed: false
  });
  const [logoError, setLogoError] = useState(false);

  // For debugging
  React.useEffect(() => {
    console.log("Sidebar - Current user data:", currentUser);
  }, [currentUser]);

  // Fetch user profile data
  useEffect(() => {
    if (currentUser?.token) {
      fetchUserProfile();
    }
  }, [currentUser]);

  const fetchUserProfile = async () => {
    if (!currentUser?.token) {
      console.log('No authentication token available');
      return;
    }

    try {
      const response = await axios.get('http://localhost:8000/profile', {
        headers: { Authorization: `Bearer ${currentUser.token}` }
      });
      console.log('Sidebar - Profile API response:', response.data);
      console.log('Sidebar - Logo URL:', response.data.logo_url);
      setUserData(response.data);
      setLogoError(false); // Reset logo error when new data is fetched
    } catch (error) {
      console.error('Error fetching profile in sidebar:', error);
    }
  };

  // Handle logo click to navigate to profile settings
  const handleLogoClick = () => {
    navigate('/dashboard?section=profile');
  };

  // Get company name with fallbacks
  const getCompanyName = () => {
    if (currentUser?.company_name) {
      return currentUser.company_name;
    }

    if (currentUser?.username) {
      return currentUser.username;
    }

    if (currentUser?.email) {
      return currentUser.email;
    }

    return "User";
  };

  // Function to scroll to top when clicking on sidebar links
  const handleLinkClick = () => {
    // Force scroll to top with multiple approaches
    window.scrollTo(0, 0);
    window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
    document.documentElement.scrollTop = 0;
    document.body.scrollTop = 0;

    // Also set a timeout to scroll again after navigation
    setTimeout(() => {
      window.scrollTo(0, 0);
      document.documentElement.scrollTop = 0;
      document.body.scrollTop = 0;
    }, 100);
  };

  // Check if the current path matches the link
  const isActive = (path) => {
    if (path === '/dashboard') {
      // Dashboard is active only if the path is /dashboard and there's no section parameter
      return location.pathname === path && !location.search.includes('section=');
    }
    return location.pathname === path;
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <ProductBranding
          variant="sidebar"
          logoSize="xlarge"
        />
      </div>

      <div className="sidebar-menu">
        <Link to="/dashboard" className={`sidebar-item ${isActive('/dashboard') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faTachometerAlt ? (
            <FontAwesomeIcon icon={faTachometerAlt} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">📊</span>
          )}
          <span>Dashboard</span>
        </Link>

        <Link to="/customers" className={`sidebar-item ${isActive('/customers') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faUsers ? (
            <FontAwesomeIcon icon={faUsers} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">👥</span>
          )}
          <span>Customers</span>
        </Link>

        <Link to="/products" className={`sidebar-item ${isActive('/products') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faBoxes ? (
            <FontAwesomeIcon icon={faBoxes} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">📦</span>
          )}
          <span>Products</span>
        </Link>

        <Link to="/invoices" className={`sidebar-item ${isActive('/invoices') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faFileInvoice ? (
            <FontAwesomeIcon icon={faFileInvoice} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">📄</span>
          )}
          <span>Invoices</span>
        </Link>

        <Link to="/reports" className={`sidebar-item ${isActive('/reports') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faChartBar ? (
            <FontAwesomeIcon icon={faChartBar} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">📈</span>
          )}
          <span>Reports</span>
        </Link>

        <Link to="/dashboard?section=invoice-template" className={`sidebar-item ${location.search.includes('section=invoice-template') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faFileAlt ? (
            <FontAwesomeIcon icon={faFileAlt} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">📄</span>
          )}
          <span>Invoice Templates</span>
        </Link>

        <Link to="/dashboard?section=business-tools" className={`sidebar-item ${location.search.includes('section=business-tools') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faTools ? (
            <FontAwesomeIcon icon={faTools} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">🔧</span>
          )}
          <span>Business Tools</span>
        </Link>

        <Link to="/dashboard?section=profile" className={`sidebar-item ${location.search.includes('section=profile') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faCog ? (
            <FontAwesomeIcon icon={faCog} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">⚙️</span>
          )}
          <span>Profile Settings</span>
        </Link>

        <Link to="/dashboard?section=notifications" className={`sidebar-item ${location.search.includes('section=notifications') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faBell ? (
            <FontAwesomeIcon icon={faBell} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">🔔</span>
          )}
          <span>Notifications</span>
        </Link>

        <Link to="/subscription" className={`sidebar-item ${isActive('/subscription') ? 'active' : ''}`} onClick={handleLinkClick}>
          {FontAwesomeIcon && faCrown ? (
            <FontAwesomeIcon icon={faCrown} className="sidebar-icon" />
          ) : (
            <span className="sidebar-icon">👑</span>
          )}
          <span>Subscription</span>
        </Link>
      </div>

      <div className="sidebar-footer">
        <div className="sidebar-user">
          <div
            className={`user-avatar ${userData?.logo_url ? 'has-logo' : 'has-letter'}`}
            onClick={handleLogoClick}
            style={{ cursor: 'pointer', position: 'relative' }}
            title="Click to go to Profile Settings"
          >
            {userData?.logo_url && !logoError ? (
              <img
                src={`http://localhost:8000${userData.logo_url}`}
                alt="Company Logo"
                className="sidebar-avatar-logo"
                onError={(e) => {
                  console.error('Logo failed to load:', userData.logo_url);
                  setLogoError(true);
                }}
                onLoad={() => {
                  console.log('Logo loaded successfully:', userData.logo_url);
                  setLogoError(false);
                }}
              />
            ) : (
              <span className="sidebar-avatar-letter">
                {userData?.company_name?.charAt(0) || currentUser?.company_name?.charAt(0) || currentUser?.username?.charAt(0) || currentUser?.email?.charAt(0) || 'U'}
              </span>
            )}
            {/* Profile completion indicator */}
            {userData?.profile_completion_percentage !== undefined && (
              <div
                className="profile-completion-indicator"
                style={{
                  position: 'absolute',
                  bottom: '-2px',
                  right: '-2px',
                  width: '20px',
                  height: '20px',
                  borderRadius: '50%',
                  backgroundColor: userData.profile_completed ? '#28a745' : userData.profile_completion_percentage >= 50 ? '#ffc107' : '#dc3545',
                  color: 'white',
                  fontSize: '10px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px solid #fff',
                  fontWeight: 'bold'
                }}
                title={`Profile ${userData.profile_completion_percentage}% complete`}
              >
                {userData.profile_completion_percentage}%
              </div>
            )}
          </div>
          <div className="user-info">
            <div className="user-name">{getCompanyName()}</div>
            {userData?.profile_completion_percentage !== undefined && !userData.profile_completed && (
              <div className="profile-completion-text" style={{ fontSize: '0.7rem', color: 'rgba(255, 255, 255, 0.7)' }}>
                Complete profile to create invoices
              </div>
            )}
          </div>
        </div>

        <button className="logout-button" onClick={onLogout}>
          {FontAwesomeIcon && faSignOutAlt ? (
            <FontAwesomeIcon icon={faSignOutAlt} className="me-2" />
          ) : (
            <span className="me-2">🚪</span>
          )}
          Logout
        </button>
      </div>
    </div>
  );
}
