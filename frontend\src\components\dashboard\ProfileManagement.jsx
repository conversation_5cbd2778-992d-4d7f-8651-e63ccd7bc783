import React, { useState, useEffect } from 'react';
import { Card, Form, Button, Row, Col, Image, Nav, Tab, ProgressBar, Badge } from 'react-bootstrap';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import { API_BASE_URL } from '../../config';

export default function ProfileManagement() {
  const { currentUser } = useAuth();
  const [userData, setUserData] = useState({
    username: '',
    email: '',
    company_name: '',
    mobile_number: '',
    address: '',
    state: '',
    state_code: '',
    gstin: '',
    bank_name: '',
    account_number: '',
    branch: '',
    ifsc_code: '',
    profile_completed: false,
    profile_completion_percentage: 0
  });

  const [logo, setLogo] = useState(null);
  const [signature, setSignature] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);
  const [signaturePreview, setSignaturePreview] = useState(null);
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const { showError, showSuccess, showWarning } = useToast();

  useEffect(() => {
    if (currentUser?.token) {
      fetchUserProfile();
    }
  }, [currentUser]);

  const fetchUserProfile = async () => {
    if (!currentUser?.token) {
      console.log('No authentication token available');
      return;
    }

    try {
      const response = await axios.get(`${API_BASE_URL}/profile`, {
        headers: { Authorization: `Bearer ${currentUser.token}` }
      });
      setUserData(response.data);

      // Check if user has logo and signature
      if (response.data.logo_url) {
        setLogoPreview(response.data.logo_url);
      }

      if (response.data.signature_url) {
        setSignaturePreview(response.data.signature_url);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      showError('Failed to load profile data');
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUserData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setLogo(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSignatureChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSignature(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setSignaturePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const updateProfile = async (e) => {
    e.preventDefault();

    if (!currentUser?.token) {
      showError('Authentication required');
      return;
    }

    try {
      const formData = new FormData();

      // Append user data
      Object.keys(userData).forEach(key => {
        formData.append(key, userData[key]);
      });

      // Append files if they exist
      if (logo) {
        formData.append('logo', logo);
      }

      if (signature) {
        formData.append('signature', signature);
      }

      const response = await axios.put(`${API_BASE_URL}/profile`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${currentUser.token}`
        }
      });

      // Update profile completion data
      if (response.data.profile_completion_percentage !== undefined) {
        setUserData(prev => ({
          ...prev,
          profile_completion_percentage: response.data.profile_completion_percentage,
          profile_completed: response.data.profile_completed
        }));
      }

      showSuccess('Profile updated successfully');
    } catch (error) {
      console.error('Error updating profile:', error);
      showError('Failed to update profile');
    }
  };

  const updatePassword = async (e) => {
    e.preventDefault();

    if (!currentUser?.token) {
      showError('Authentication required');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      showError('New passwords do not match');
      return;
    }

    try {
      await axios.put(`${API_BASE_URL}/change-password`, {
        current_password: passwordData.currentPassword,
        new_password: passwordData.newPassword
      }, {
        headers: { Authorization: `Bearer ${currentUser.token}` }
      });

      showSuccess('Password updated successfully');

      // Clear password fields
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (error) {
      console.error('Error updating password:', error);
      showError(error.response?.data?.detail || 'Failed to update password');
    }
  };

  // For debugging
  useEffect(() => {
    console.log("Profile Management - Current user data:", currentUser);
    console.log("Profile Management - User data:", userData);
  }, [currentUser, userData]);

  // Get company name with fallbacks
  const getCompanyName = () => {
    // First try userData.company_name which comes from profile API
    if (userData?.company_name) {
      console.log("Using company name from userData:", userData.company_name);
      return userData.company_name;
    }

    // Then try currentUser.company_name which comes from JWT token or localStorage
    if (currentUser?.company_name) {
      console.log("Using company name from currentUser:", currentUser.company_name);
      return currentUser.company_name;
    }

    // Fallbacks
    if (currentUser?.username) {
      return currentUser.username;
    }

    if (currentUser?.email) {
      return currentUser.email;
    }

    return "User";
  };

  const companyName = getCompanyName();
  console.log("Profile - Final company name to display:", companyName);

  return (
    <div className="profile-management-container">
      {/* Profile Completion Progress */}
      <div className="profile-completion-section mb-4">
        <Card className="profile-completion-card">
          <Card.Body>
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h5 className="mb-0">
                <i className="fas fa-chart-line me-2 text-primary"></i>
                Profile Completion
              </h5>
              <Badge
                bg={userData.profile_completed ? 'success' : userData.profile_completion_percentage >= 50 ? 'warning' : 'danger'}
                className="fs-6"
              >
                {userData.profile_completion_percentage}%
              </Badge>
            </div>
            <ProgressBar
              now={userData.profile_completion_percentage}
              variant={userData.profile_completed ? 'success' : userData.profile_completion_percentage >= 50 ? 'warning' : 'danger'}
              style={{ height: '10px' }}
            />
            <div className="mt-2">
              {userData.profile_completed ? (
                <small className="text-success">
                  <i className="fas fa-check-circle me-1"></i>
                  Profile complete! You can create invoices.
                </small>
              ) : (
                <small className="text-warning">
                  <i className="fas fa-exclamation-triangle me-1"></i>
                  Complete Personal Info and Company Details to create invoices.
                </small>
              )}
            </div>
          </Card.Body>
        </Card>
      </div>

        <Tab.Container id="profile-tabs" defaultActiveKey="personal">
          <Nav variant="tabs" className="profile-tabs mb-4">
            <Nav.Item>
              <Nav.Link eventKey="personal" className="profile-tab">
                <i className="fas fa-user me-2"></i>Personal Info
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="company" className="profile-tab">
                <i className="fas fa-building me-2"></i>Company Details
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="branding" className="profile-tab">
                <i className="fas fa-image me-2"></i>Branding
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="bank" className="profile-tab">
                <i className="fas fa-university me-2"></i>Bank Details
              </Nav.Link>
            </Nav.Item>
            <Nav.Item>
              <Nav.Link eventKey="security" className="profile-tab">
                <i className="fas fa-lock me-2"></i>Security
              </Nav.Link>
            </Nav.Item>
          </Nav>

          <Tab.Content>
            <Tab.Pane eventKey="personal" className="profile-tab-content">
              <Form onSubmit={updateProfile} className="profile-form">
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3" controlId="username">
                      <Form.Label><i className="fas fa-user-tag me-2 text-primary"></i>Username</Form.Label>
                      <Form.Control
                        type="text"
                        name="username"
                        value={userData.username}
                        onChange={handleInputChange}
                        disabled
                        className="profile-input disabled"
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3" controlId="email">
                      <Form.Label><i className="fas fa-envelope me-2 text-primary"></i>Email</Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        value={userData.email}
                        onChange={handleInputChange}
                        disabled
                        className="profile-input disabled"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3" controlId="mobile_number">
                      <Form.Label><i className="fas fa-mobile-alt me-2 text-primary"></i>Mobile Number</Form.Label>
                      <Form.Control
                        type="text"
                        name="mobile_number"
                        value={userData.mobile_number}
                        onChange={handleInputChange}
                        className="profile-input"
                        placeholder="Enter your mobile number"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <div className="d-flex justify-content-end mt-4">
                  <Button variant="primary" type="submit" className="profile-save-btn">
                    <i className="fas fa-save me-2"></i>Save Personal Info
                  </Button>
                </div>
              </Form>
            </Tab.Pane>

            <Tab.Pane eventKey="branding" className="profile-tab-content">
              <div className="branding-intro mb-4">
                <h5 className="mb-2"><i className="fas fa-info-circle me-2 text-primary"></i>Company Branding</h5>
                <p className="text-muted">Upload your company logo and digital signature to be used on invoices and other documents.</p>
              </div>

              <Row>
                <Col md={6} className="text-center mb-4">
                  <div className="profile-upload-section mb-4">
                    <div className="profile-image-container">
                      {logoPreview ? (
                        <Image src={logoPreview} alt="Company Logo" className="profile-image" />
                      ) : (
                        <div className="profile-image profile-placeholder d-flex align-items-center justify-content-center">
                          <i className="fas fa-building fa-2x"></i>
                        </div>
                      )}
                      <div className="profile-image-overlay">
                        <label htmlFor="logo" className="upload-icon">
                          <i className="fas fa-camera"></i>
                        </label>
                      </div>
                    </div>
                    <h6 className="profile-upload-title">Company Logo</h6>
                    <p className="profile-upload-desc">Upload a company logo for your invoices</p>
                    <Form.Group controlId="logo" className="file-input-container">
                      <Form.Control
                        type="file"
                        onChange={handleLogoChange}
                        accept="image/*"
                        className="file-input"
                      />
                      <Button variant="outline-primary" className="upload-btn" onClick={() => document.getElementById('logo').click()}>
                        <i className="fas fa-upload me-2"></i>Choose Logo
                      </Button>
                    </Form.Group>
                  </div>
                </Col>

                <Col md={6} className="text-center mb-4">
                  <div className="profile-upload-section">
                    <div className="signature-container">
                      {signaturePreview ? (
                        <Image src={signaturePreview} alt="Signature" className="signature-image" />
                      ) : (
                        <div className="signature-image signature-placeholder d-flex align-items-center justify-content-center">
                          <i className="fas fa-signature"></i>
                        </div>
                      )}
                      <div className="signature-overlay">
                        <label htmlFor="signature" className="upload-icon">
                          <i className="fas fa-pen"></i>
                        </label>
                      </div>
                    </div>
                    <h6 className="profile-upload-title">Digital Signature</h6>
                    <p className="profile-upload-desc">Upload your signature for invoices</p>
                    <Form.Group controlId="signature" className="file-input-container">
                      <Form.Control
                        type="file"
                        onChange={handleSignatureChange}
                        accept="image/*"
                        className="file-input"
                      />
                      <Button variant="outline-primary" className="upload-btn" onClick={() => document.getElementById('signature').click()}>
                        <i className="fas fa-upload me-2"></i>Choose Signature
                      </Button>
                    </Form.Group>
                  </div>
                </Col>
              </Row>

              <div className="d-flex justify-content-end mt-4">
                <Button variant="primary" onClick={updateProfile} className="profile-save-btn">
                  <i className="fas fa-save me-2"></i>Save Branding
                </Button>
              </div>
            </Tab.Pane>

      <Tab.Pane eventKey="company" className="profile-tab-content">
        <Form onSubmit={updateProfile} className="profile-form">
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="company_name">
                <Form.Label><i className="fas fa-building me-2 text-primary"></i>Company Name</Form.Label>
                <Form.Control
                  type="text"
                  name="company_name"
                  value={userData.company_name}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter company name"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="gstin">
                <Form.Label><i className="fas fa-id-card me-2 text-primary"></i>GSTIN</Form.Label>
                <Form.Control
                  type="text"
                  name="gstin"
                  value={userData.gstin}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter GSTIN"
                />
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3" controlId="address">
            <Form.Label><i className="fas fa-map-marker-alt me-2 text-primary"></i>Address</Form.Label>
            <Form.Control
              as="textarea"
              rows={2}
              name="address"
              value={userData.address}
              onChange={handleInputChange}
              className="profile-input"
              placeholder="Enter company address"
            />
          </Form.Group>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="state">
                <Form.Label><i className="fas fa-map me-2 text-primary"></i>State</Form.Label>
                <Form.Control
                  type="text"
                  name="state"
                  value={userData.state}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter state"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="state_code">
                <Form.Label><i className="fas fa-code me-2 text-primary"></i>State Code</Form.Label>
                <Form.Control
                  type="text"
                  name="state_code"
                  value={userData.state_code}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter state code"
                />
              </Form.Group>
            </Col>
          </Row>

          <div className="d-flex justify-content-end mt-4">
            <Button variant="primary" type="submit" className="profile-save-btn">
              <i className="fas fa-save me-2"></i>Save Company Details
            </Button>
          </div>
        </Form>
      </Tab.Pane>

      <Tab.Pane eventKey="bank" className="profile-tab-content">
        <Form onSubmit={updateProfile} className="profile-form">
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="bank_name">
                <Form.Label><i className="fas fa-university me-2 text-primary"></i>Bank Name</Form.Label>
                <Form.Control
                  type="text"
                  name="bank_name"
                  value={userData.bank_name}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter bank name"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="account_number">
                <Form.Label><i className="fas fa-credit-card me-2 text-primary"></i>Account Number</Form.Label>
                <Form.Control
                  type="text"
                  name="account_number"
                  value={userData.account_number}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter account number"
                />
              </Form.Group>
            </Col>
          </Row>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="branch">
                <Form.Label><i className="fas fa-code-branch me-2 text-primary"></i>Branch</Form.Label>
                <Form.Control
                  type="text"
                  name="branch"
                  value={userData.branch}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter branch name"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="ifsc_code">
                <Form.Label><i className="fas fa-qrcode me-2 text-primary"></i>IFSC Code</Form.Label>
                <Form.Control
                  type="text"
                  name="ifsc_code"
                  value={userData.ifsc_code}
                  onChange={handleInputChange}
                  className="profile-input"
                  placeholder="Enter IFSC code"
                />
              </Form.Group>
            </Col>
          </Row>

          <div className="d-flex justify-content-end mt-4">
            <Button variant="primary" type="submit" className="profile-save-btn">
              <i className="fas fa-save me-2"></i>Save Bank Details
            </Button>
          </div>
        </Form>
      </Tab.Pane>

      <Tab.Pane eventKey="security" className="profile-tab-content">
        <Form onSubmit={updatePassword} className="profile-form">
          <div className="security-section">
            <div className="security-icon">
              <i className="fas fa-lock"></i>
            </div>
            <div className="security-content">
              <h5 className="security-title">Change Password</h5>
              <p className="security-desc">Ensure your account is using a strong password for better security</p>
            </div>
          </div>

          <Form.Group className="mb-3" controlId="currentPassword">
            <Form.Label><i className="fas fa-key me-2 text-primary"></i>Current Password</Form.Label>
            <Form.Control
              type="password"
              name="currentPassword"
              value={passwordData.currentPassword}
              onChange={handlePasswordChange}
              required
              className="profile-input"
              placeholder="Enter current password"
            />
          </Form.Group>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="newPassword">
                <Form.Label><i className="fas fa-lock me-2 text-primary"></i>New Password</Form.Label>
                <Form.Control
                  type="password"
                  name="newPassword"
                  value={passwordData.newPassword}
                  onChange={handlePasswordChange}
                  required
                  className="profile-input"
                  placeholder="Enter new password"
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3" controlId="confirmPassword">
                <Form.Label><i className="fas fa-check-circle me-2 text-primary"></i>Confirm New Password</Form.Label>
                <Form.Control
                  type="password"
                  name="confirmPassword"
                  value={passwordData.confirmPassword}
                  onChange={handlePasswordChange}
                  required
                  className="profile-input"
                  placeholder="Confirm new password"
                />
              </Form.Group>
            </Col>
          </Row>

          <div className="d-flex justify-content-end mt-4">
            <Button variant="danger" type="submit" className="profile-save-btn">
              <i className="fas fa-key me-2"></i>Update Password
            </Button>
          </div>
        </Form>
      </Tab.Pane>
          </Tab.Content>
        </Tab.Container>
    </div>
  );
}
