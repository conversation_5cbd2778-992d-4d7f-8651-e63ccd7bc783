{"ast": null, "code": "var _jsxFileName = \"D:\\\\invoice_project\\\\gst-billing-software\\\\frontend\\\\src\\\\pages\\\\auth\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, But<PERSON>, Card, Container, Row, Col } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useToast } from '../../context/ToastContext';\nimport ProductBranding from '../../components/common/ProductBranding';\nimport '../../styles/auth.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function Register() {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    confirmPassword: '',\n    company_name: '',\n    mobile_number: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const {\n    register\n  } = useAuth();\n  const {\n    showError,\n    showSuccess\n  } = useToast();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const validateEmail = email => {\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    return emailRegex.test(email);\n  };\n  const validateMobileNumber = mobile => {\n    return /^\\d{10}$/.test(mobile);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validation\n    if (!validateEmail(formData.email)) {\n      return showError('Please enter a valid email address');\n    }\n    if (!validateMobileNumber(formData.mobile_number)) {\n      return showError('Mobile number must be exactly 10 digits');\n    }\n    if (formData.password.length < 6) {\n      return showError('Password should be at least 6 characters');\n    }\n    if (formData.password !== formData.confirmPassword) {\n      return showError('Passwords do not match');\n    }\n    if (!formData.company_name.trim()) {\n      return showError('Company name is required');\n    }\n    try {\n      setLoading(true);\n\n      // Send the registration data directly\n      await register(formData);\n      showSuccess('Account created successfully! Welcome to SwiftBiller.');\n      navigate('/dashboard');\n    } catch (err) {\n      console.error('Registration error:', err);\n\n      // Handle different types of errors\n      if (err.response) {\n        const errorData = err.response.data;\n\n        // Handle validation errors (422 status)\n        if (err.response.status === 422 && errorData.detail) {\n          if (Array.isArray(errorData.detail)) {\n            // Pydantic validation errors\n            const errorMessages = errorData.detail.map(error => {\n              const field = error.loc ? error.loc[error.loc.length - 1] : 'field';\n              return `${field}: ${error.msg}`;\n            }).join(', ');\n            showError(`Validation error: ${errorMessages}`);\n          } else {\n            showError(`Validation error: ${errorData.detail}`);\n          }\n        } else if (errorData.detail) {\n          // Other API errors\n          showError(errorData.detail);\n        } else {\n          showError(`Server error (${err.response.status}): Please try again`);\n        }\n      } else if (err.request) {\n        showError('No response from server. Please check your connection and try again.');\n      } else {\n        showError(`Error: ${err.message}`);\n      }\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          lg: 10,\n          xl: 9,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"auth-card registration-card\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"auth-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"auth-logo\",\n                children: /*#__PURE__*/_jsxDEV(ProductBranding, {\n                  variant: \"centered\",\n                  logoSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"auth-title\",\n                children: \"Create Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"auth-subtitle\",\n                children: \"Join our platform to streamline your business\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"auth-body\",\n              children: [/*#__PURE__*/_jsxDEV(Form, {\n                onSubmit: handleSubmit,\n                className: \"registration-form\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"registration-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"section-title\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-user-circle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 51\n                    }, this), \"Account Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 126,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"auth-form-label\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-envelope\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 129,\n                        columnNumber: 25\n                      }, this), \"Email Address\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 128,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"email\",\n                      name: \"email\",\n                      required: true,\n                      value: formData.email,\n                      onChange: handleChange,\n                      className: \"auth-input\",\n                      placeholder: \"Enter your email address\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 131,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"This will be used as your username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-lock\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 147,\n                            columnNumber: 29\n                          }, this), \"Password\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 146,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"password\",\n                          name: \"password\",\n                          required: true,\n                          value: formData.password,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Create a password\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 149,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: \"Must be at least 6 characters\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 158,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 145,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-check-circle\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 164,\n                            columnNumber: 29\n                          }, this), \"Confirm Password\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 163,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"password\",\n                          name: \"confirmPassword\",\n                          required: true,\n                          value: formData.confirmPassword,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Confirm your password\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 166,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 162,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 125,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"registration-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"section-title\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-building me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 181,\n                      columnNumber: 51\n                    }, this), \"Basic Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 181,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-briefcase\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 186,\n                            columnNumber: 29\n                          }, this), \"Company Name\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 185,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"text\",\n                          name: \"company_name\",\n                          required: true,\n                          value: formData.company_name,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Enter your company name\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 188,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 184,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 183,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-mobile-alt\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 202,\n                            columnNumber: 29\n                          }, this), \"Mobile Number\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 201,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"tel\",\n                          name: \"mobile_number\",\n                          required: true,\n                          value: formData.mobile_number,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Enter 10-digit mobile number\",\n                          maxLength: \"10\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 204,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 200,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"alert alert-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-info-circle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 219,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Complete your profile after registration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 220,\n                      columnNumber: 80\n                    }, this), \"You'll need to add company details (address, state, GSTIN) and bank information before creating invoices.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"terms-section mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    id: \"terms-checkbox\",\n                    label: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"I agree to the \", /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"#\",\n                        className: \"auth-link\",\n                        children: \"Terms of Service\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 51\n                      }, this), \" and \", /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"#\",\n                        className: \"auth-link\",\n                        children: \"Privacy Policy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 229,\n                        columnNumber: 114\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 30\n                    }, this),\n                    className: \"auth-checkbox\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  disabled: loading,\n                  className: \"auth-btn w-100 mt-3\",\n                  type: \"submit\",\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this), \"Creating Account...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [\"Create Account \", /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-arrow-right ms-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 246,\n                      columnNumber: 40\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 124,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"auth-footer\",\n                children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"auth-link\",\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 44\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"1ILANatP0OlBcm5mhxbX9jmFDNY=\", false, function () {\n  return [useAuth, useToast, useNavigate];\n});\n_c = Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Form", "<PERSON><PERSON>", "Card", "Container", "Row", "Col", "Link", "useNavigate", "useAuth", "useToast", "ProductBranding", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "formData", "setFormData", "email", "password", "confirmPassword", "company_name", "mobile_number", "loading", "setLoading", "register", "showError", "showSuccess", "navigate", "handleChange", "e", "name", "value", "target", "validateEmail", "emailRegex", "test", "validateMobileNumber", "mobile", "handleSubmit", "preventDefault", "length", "trim", "err", "console", "error", "response", "errorData", "data", "status", "detail", "Array", "isArray", "errorMessages", "map", "field", "loc", "msg", "join", "request", "message", "className", "children", "xs", "lg", "xl", "Header", "variant", "logoSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "onSubmit", "Group", "Label", "Control", "type", "required", "onChange", "placeholder", "md", "max<PERSON><PERSON><PERSON>", "Check", "id", "label", "to", "disabled", "role", "_c", "$RefreshReg$"], "sources": ["D:/invoice_project/gst-billing-software/frontend/src/pages/auth/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON>, <PERSON><PERSON>, Card, Container, Row, Col } from 'react-bootstrap';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../../context/AuthContext';\r\nimport { useToast } from '../../context/ToastContext';\r\nimport ProductBranding from '../../components/common/ProductBranding';\r\nimport '../../styles/auth.css';\r\n\r\n\r\n\r\nexport default function Register() {\r\n  const [formData, setFormData] = useState({\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    company_name: '',\r\n    mobile_number: ''\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const { register } = useAuth();\r\n  const { showError, showSuccess } = useToast();\r\n  const navigate = useNavigate();\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value\r\n    });\r\n  };\r\n\r\n  const validateEmail = (email) => {\r\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n    return emailRegex.test(email);\r\n  };\r\n\r\n  const validateMobileNumber = (mobile) => {\r\n    return /^\\d{10}$/.test(mobile);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validation\r\n    if (!validateEmail(formData.email)) {\r\n      return showError('Please enter a valid email address');\r\n    }\r\n\r\n    if (!validateMobileNumber(formData.mobile_number)) {\r\n      return showError('Mobile number must be exactly 10 digits');\r\n    }\r\n\r\n    if (formData.password.length < 6) {\r\n      return showError('Password should be at least 6 characters');\r\n    }\r\n\r\n    if (formData.password !== formData.confirmPassword) {\r\n      return showError('Passwords do not match');\r\n    }\r\n\r\n    if (!formData.company_name.trim()) {\r\n      return showError('Company name is required');\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Send the registration data directly\r\n      await register(formData);\r\n      showSuccess('Account created successfully! Welcome to SwiftBiller.');\r\n      navigate('/dashboard');\r\n    } catch (err) {\r\n      console.error('Registration error:', err);\r\n\r\n      // Handle different types of errors\r\n      if (err.response) {\r\n        const errorData = err.response.data;\r\n\r\n        // Handle validation errors (422 status)\r\n        if (err.response.status === 422 && errorData.detail) {\r\n          if (Array.isArray(errorData.detail)) {\r\n            // Pydantic validation errors\r\n            const errorMessages = errorData.detail.map(error => {\r\n              const field = error.loc ? error.loc[error.loc.length - 1] : 'field';\r\n              return `${field}: ${error.msg}`;\r\n            }).join(', ');\r\n            showError(`Validation error: ${errorMessages}`);\r\n          } else {\r\n            showError(`Validation error: ${errorData.detail}`);\r\n          }\r\n        } else if (errorData.detail) {\r\n          // Other API errors\r\n          showError(errorData.detail);\r\n        } else {\r\n          showError(`Server error (${err.response.status}): Please try again`);\r\n        }\r\n      } else if (err.request) {\r\n        showError('No response from server. Please check your connection and try again.');\r\n      } else {\r\n        showError(`Error: ${err.message}`);\r\n      }\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <Container>\r\n        <Row className=\"justify-content-center\">\r\n          <Col xs={12} lg={10} xl={9}>\r\n            <Card className=\"auth-card registration-card\">\r\n              <Card.Header className=\"auth-header\">\r\n                <div className=\"auth-logo\">\r\n                  <ProductBranding\r\n                    variant=\"centered\"\r\n                    logoSize=\"large\"\r\n                  />\r\n                </div>\r\n                <h2 className=\"auth-title\">Create Account</h2>\r\n                <p className=\"auth-subtitle\">Join our platform to streamline your business</p>\r\n              </Card.Header>\r\n\r\n              <Card.Body className=\"auth-body\">\r\n                <Form onSubmit={handleSubmit} className=\"registration-form\">\r\n                  <div className=\"registration-section\">\r\n                    <h5 className=\"section-title\"><i className=\"fas fa-user-circle me-2\"></i>Account Information</h5>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label className=\"auth-form-label\">\r\n                        <i className=\"fas fa-envelope\"></i>Email Address\r\n                      </Form.Label>\r\n                      <Form.Control\r\n                        type=\"email\"\r\n                        name=\"email\"\r\n                        required\r\n                        value={formData.email}\r\n                        onChange={handleChange}\r\n                        className=\"auth-input\"\r\n                        placeholder=\"Enter your email address\"\r\n                      />\r\n                      <small className=\"text-muted\">This will be used as your username</small>\r\n                    </Form.Group>\r\n\r\n                    <Row>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-lock\"></i>Password\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"password\"\r\n                            name=\"password\"\r\n                            required\r\n                            value={formData.password}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Create a password\"\r\n                          />\r\n                          <small className=\"text-muted\">Must be at least 6 characters</small>\r\n                        </Form.Group>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-check-circle\"></i>Confirm Password\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"password\"\r\n                            name=\"confirmPassword\"\r\n                            required\r\n                            value={formData.confirmPassword}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Confirm your password\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n\r\n                  <div className=\"registration-section\">\r\n                    <h5 className=\"section-title\"><i className=\"fas fa-building me-2\"></i>Basic Information</h5>\r\n                    <Row>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-briefcase\"></i>Company Name\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"text\"\r\n                            name=\"company_name\"\r\n                            required\r\n                            value={formData.company_name}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Enter your company name\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-mobile-alt\"></i>Mobile Number\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"tel\"\r\n                            name=\"mobile_number\"\r\n                            required\r\n                            value={formData.mobile_number}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Enter 10-digit mobile number\"\r\n                            maxLength=\"10\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    <div className=\"alert alert-info\">\r\n                      <i className=\"fas fa-info-circle me-2\"></i>\r\n                      <strong>Complete your profile after registration</strong><br />\r\n                      You'll need to add company details (address, state, GSTIN) and bank information before creating invoices.\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"terms-section mb-4\">\r\n                    <Form.Check\r\n                      type=\"checkbox\"\r\n                      id=\"terms-checkbox\"\r\n                      label={<span>I agree to the <Link to=\"#\" className=\"auth-link\">Terms of Service</Link> and <Link to=\"#\" className=\"auth-link\">Privacy Policy</Link></span>}\r\n                      className=\"auth-checkbox\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <Button\r\n                    disabled={loading}\r\n                    className=\"auth-btn w-100 mt-3\"\r\n                    type=\"submit\"\r\n                  >\r\n                    {loading ? (\r\n                      <>\r\n                        <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                        Creating Account...\r\n                      </>\r\n                    ) : (\r\n                      <>Create Account <i className=\"fas fa-arrow-right ms-2\"></i></>\r\n                    )}\r\n                  </Button>\r\n                </Form>\r\n\r\n                <div className=\"auth-footer\">\r\n                  Already have an account? <Link to=\"/login\" className=\"auth-link\">Sign In</Link>\r\n                </div>\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACzE,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI/B,eAAe,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE2B;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC9B,MAAM;IAAEmB,SAAS;IAAEC;EAAY,CAAC,GAAGnB,QAAQ,CAAC,CAAC;EAC7C,MAAMoB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAMuB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACe,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,aAAa,GAAIhB,KAAK,IAAK;IAC/B,MAAMiB,UAAU,GAAG,kDAAkD;IACrE,OAAOA,UAAU,CAACC,IAAI,CAAClB,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMmB,oBAAoB,GAAIC,MAAM,IAAK;IACvC,OAAO,UAAU,CAACF,IAAI,CAACE,MAAM,CAAC;EAChC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACN,aAAa,CAAClB,QAAQ,CAACE,KAAK,CAAC,EAAE;MAClC,OAAOQ,SAAS,CAAC,oCAAoC,CAAC;IACxD;IAEA,IAAI,CAACW,oBAAoB,CAACrB,QAAQ,CAACM,aAAa,CAAC,EAAE;MACjD,OAAOI,SAAS,CAAC,yCAAyC,CAAC;IAC7D;IAEA,IAAIV,QAAQ,CAACG,QAAQ,CAACsB,MAAM,GAAG,CAAC,EAAE;MAChC,OAAOf,SAAS,CAAC,0CAA0C,CAAC;IAC9D;IAEA,IAAIV,QAAQ,CAACG,QAAQ,KAAKH,QAAQ,CAACI,eAAe,EAAE;MAClD,OAAOM,SAAS,CAAC,wBAAwB,CAAC;IAC5C;IAEA,IAAI,CAACV,QAAQ,CAACK,YAAY,CAACqB,IAAI,CAAC,CAAC,EAAE;MACjC,OAAOhB,SAAS,CAAC,0BAA0B,CAAC;IAC9C;IAEA,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMC,QAAQ,CAACT,QAAQ,CAAC;MACxBW,WAAW,CAAC,uDAAuD,CAAC;MACpEC,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOe,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,qBAAqB,EAAEF,GAAG,CAAC;;MAEzC;MACA,IAAIA,GAAG,CAACG,QAAQ,EAAE;QAChB,MAAMC,SAAS,GAAGJ,GAAG,CAACG,QAAQ,CAACE,IAAI;;QAEnC;QACA,IAAIL,GAAG,CAACG,QAAQ,CAACG,MAAM,KAAK,GAAG,IAAIF,SAAS,CAACG,MAAM,EAAE;UACnD,IAAIC,KAAK,CAACC,OAAO,CAACL,SAAS,CAACG,MAAM,CAAC,EAAE;YACnC;YACA,MAAMG,aAAa,GAAGN,SAAS,CAACG,MAAM,CAACI,GAAG,CAACT,KAAK,IAAI;cAClD,MAAMU,KAAK,GAAGV,KAAK,CAACW,GAAG,GAAGX,KAAK,CAACW,GAAG,CAACX,KAAK,CAACW,GAAG,CAACf,MAAM,GAAG,CAAC,CAAC,GAAG,OAAO;cACnE,OAAO,GAAGc,KAAK,KAAKV,KAAK,CAACY,GAAG,EAAE;YACjC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;YACbhC,SAAS,CAAC,qBAAqB2B,aAAa,EAAE,CAAC;UACjD,CAAC,MAAM;YACL3B,SAAS,CAAC,qBAAqBqB,SAAS,CAACG,MAAM,EAAE,CAAC;UACpD;QACF,CAAC,MAAM,IAAIH,SAAS,CAACG,MAAM,EAAE;UAC3B;UACAxB,SAAS,CAACqB,SAAS,CAACG,MAAM,CAAC;QAC7B,CAAC,MAAM;UACLxB,SAAS,CAAC,iBAAiBiB,GAAG,CAACG,QAAQ,CAACG,MAAM,qBAAqB,CAAC;QACtE;MACF,CAAC,MAAM,IAAIN,GAAG,CAACgB,OAAO,EAAE;QACtBjC,SAAS,CAAC,sEAAsE,CAAC;MACnF,CAAC,MAAM;QACLA,SAAS,CAAC,UAAUiB,GAAG,CAACiB,OAAO,EAAE,CAAC;MACpC;IACF;IACApC,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEb,OAAA;IAAKkD,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BnD,OAAA,CAACT,SAAS;MAAA4D,QAAA,eACRnD,OAAA,CAACR,GAAG;QAAC0D,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCnD,OAAA,CAACP,GAAG;UAAC2D,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAH,QAAA,eACzBnD,OAAA,CAACV,IAAI;YAAC4D,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC3CnD,OAAA,CAACV,IAAI,CAACiE,MAAM;cAACL,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAClCnD,OAAA;gBAAKkD,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBnD,OAAA,CAACF,eAAe;kBACd0D,OAAO,EAAC,UAAU;kBAClBC,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7D,OAAA;gBAAIkD,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9C7D,OAAA;gBAAGkD,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAA6C;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEd7D,OAAA,CAACV,IAAI,CAACwE,IAAI;cAACZ,SAAS,EAAC,WAAW;cAAAC,QAAA,gBAC9BnD,OAAA,CAACZ,IAAI;gBAAC2E,QAAQ,EAAEnC,YAAa;gBAACsB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBACzDnD,OAAA;kBAAKkD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCnD,OAAA;oBAAIkD,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAACnD,OAAA;sBAAGkD,SAAS,EAAC;oBAAyB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uBAAmB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjG7D,OAAA,CAACZ,IAAI,CAAC4E,KAAK;oBAACd,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1BnD,OAAA,CAACZ,IAAI,CAAC6E,KAAK;sBAACf,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBACrCnD,OAAA;wBAAGkD,SAAS,EAAC;sBAAiB;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,iBACrC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,IAAI,CAAC8E,OAAO;sBACXC,IAAI,EAAC,OAAO;sBACZ/C,IAAI,EAAC,OAAO;sBACZgD,QAAQ;sBACR/C,KAAK,EAAEhB,QAAQ,CAACE,KAAM;sBACtB8D,QAAQ,EAAEnD,YAAa;sBACvBgC,SAAS,EAAC,YAAY;sBACtBoB,WAAW,EAAC;oBAA0B;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACF7D,OAAA;sBAAOkD,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAkC;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eAEb7D,OAAA,CAACR,GAAG;oBAAA2D,QAAA,gBACFnD,OAAA,CAACP,GAAG;sBAAC8E,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACTnD,OAAA,CAACZ,IAAI,CAAC4E,KAAK;wBAACd,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BnD,OAAA,CAACZ,IAAI,CAAC6E,KAAK;0BAACf,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCnD,OAAA;4BAAGkD,SAAS,EAAC;0BAAa;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,YACjC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,IAAI,CAAC8E,OAAO;0BACXC,IAAI,EAAC,UAAU;0BACf/C,IAAI,EAAC,UAAU;0BACfgD,QAAQ;0BACR/C,KAAK,EAAEhB,QAAQ,CAACG,QAAS;0BACzB6D,QAAQ,EAAEnD,YAAa;0BACvBgC,SAAS,EAAC,YAAY;0BACtBoB,WAAW,EAAC;wBAAmB;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC,eACF7D,OAAA;0BAAOkD,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAA6B;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN7D,OAAA,CAACP,GAAG;sBAAC8E,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACTnD,OAAA,CAACZ,IAAI,CAAC4E,KAAK;wBAACd,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BnD,OAAA,CAACZ,IAAI,CAAC6E,KAAK;0BAACf,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCnD,OAAA;4BAAGkD,SAAS,EAAC;0BAAqB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,oBACzC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,IAAI,CAAC8E,OAAO;0BACXC,IAAI,EAAC,UAAU;0BACf/C,IAAI,EAAC,iBAAiB;0BACtBgD,QAAQ;0BACR/C,KAAK,EAAEhB,QAAQ,CAACI,eAAgB;0BAChC4D,QAAQ,EAAEnD,YAAa;0BACvBgC,SAAS,EAAC,YAAY;0BACtBoB,WAAW,EAAC;wBAAuB;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7D,OAAA;kBAAKkD,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCnD,OAAA;oBAAIkD,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAACnD,OAAA;sBAAGkD,SAAS,EAAC;oBAAsB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,qBAAiB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5F7D,OAAA,CAACR,GAAG;oBAAA2D,QAAA,gBACFnD,OAAA,CAACP,GAAG;sBAAC8E,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACTnD,OAAA,CAACZ,IAAI,CAAC4E,KAAK;wBAACd,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BnD,OAAA,CAACZ,IAAI,CAAC6E,KAAK;0BAACf,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCnD,OAAA;4BAAGkD,SAAS,EAAC;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,gBACtC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,IAAI,CAAC8E,OAAO;0BACXC,IAAI,EAAC,MAAM;0BACX/C,IAAI,EAAC,cAAc;0BACnBgD,QAAQ;0BACR/C,KAAK,EAAEhB,QAAQ,CAACK,YAAa;0BAC7B2D,QAAQ,EAAEnD,YAAa;0BACvBgC,SAAS,EAAC,YAAY;0BACtBoB,WAAW,EAAC;wBAAyB;0BAAAZ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACN7D,OAAA,CAACP,GAAG;sBAAC8E,EAAE,EAAE,CAAE;sBAAApB,QAAA,eACTnD,OAAA,CAACZ,IAAI,CAAC4E,KAAK;wBAACd,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BnD,OAAA,CAACZ,IAAI,CAAC6E,KAAK;0BAACf,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCnD,OAAA;4BAAGkD,SAAS,EAAC;0BAAmB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,iBACvC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACb7D,OAAA,CAACZ,IAAI,CAAC8E,OAAO;0BACXC,IAAI,EAAC,KAAK;0BACV/C,IAAI,EAAC,eAAe;0BACpBgD,QAAQ;0BACR/C,KAAK,EAAEhB,QAAQ,CAACM,aAAc;0BAC9B0D,QAAQ,EAAEnD,YAAa;0BACvBgC,SAAS,EAAC,YAAY;0BACtBoB,WAAW,EAAC,8BAA8B;0BAC1CE,SAAS,EAAC;wBAAI;0BAAAd,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN7D,OAAA;oBAAKkD,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BnD,OAAA;sBAAGkD,SAAS,EAAC;oBAAyB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3C7D,OAAA;sBAAAmD,QAAA,EAAQ;oBAAwC;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAAA7D,OAAA;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,6GAEjE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN7D,OAAA;kBAAKkD,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjCnD,OAAA,CAACZ,IAAI,CAACqF,KAAK;oBACTN,IAAI,EAAC,UAAU;oBACfO,EAAE,EAAC,gBAAgB;oBACnBC,KAAK,eAAE3E,OAAA;sBAAAmD,QAAA,GAAM,iBAAe,eAAAnD,OAAA,CAACN,IAAI;wBAACkF,EAAE,EAAC,GAAG;wBAAC1B,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,SAAK,eAAA7D,OAAA,CAACN,IAAI;wBAACkF,EAAE,EAAC,GAAG;wBAAC1B,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAc;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAE;oBAC3JX,SAAS,EAAC,eAAe;oBACzBkB,QAAQ;kBAAA;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN7D,OAAA,CAACX,MAAM;kBACLwF,QAAQ,EAAEjE,OAAQ;kBAClBsC,SAAS,EAAC,qBAAqB;kBAC/BiB,IAAI,EAAC,QAAQ;kBAAAhB,QAAA,EAEZvC,OAAO,gBACNZ,OAAA,CAAAE,SAAA;oBAAAiD,QAAA,gBACEnD,OAAA;sBAAMkD,SAAS,EAAC,uCAAuC;sBAAC4B,IAAI,EAAC,QAAQ;sBAAC,eAAY;oBAAM;sBAAApB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,uBAElG;kBAAA,eAAE,CAAC,gBAEH7D,OAAA,CAAAE,SAAA;oBAAAiD,QAAA,GAAE,iBAAe,eAAAnD,OAAA;sBAAGkD,SAAS,EAAC;oBAAyB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,eAAE;gBAC/D;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEP7D,OAAA;gBAAKkD,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,2BACF,eAAAnD,OAAA,CAACN,IAAI;kBAACkF,EAAE,EAAC,QAAQ;kBAAC1B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAACzD,EAAA,CA1PuBD,QAAQ;EAAA,QASTP,OAAO,EACOC,QAAQ,EAC1BF,WAAW;AAAA;AAAAoF,EAAA,GAXN5E,QAAQ;AAAA,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}