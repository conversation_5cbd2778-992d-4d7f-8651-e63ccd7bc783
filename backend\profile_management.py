from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import Optional
from models import User
from database import get_db
from auth import get_current_user, get_password_hash, verify_password
import os
import shutil
from datetime import datetime

router = APIRouter()

# Create uploads directory if it doesn't exist
UPLOAD_DIR = "uploads"
LOGO_DIR = os.path.join(UPLOAD_DIR, "logos")
SIGNATURE_DIR = os.path.join(UPLOAD_DIR, "signatures")

os.makedirs(LOGO_DIR, exist_ok=True)
os.makedirs(SIGNATURE_DIR, exist_ok=True)

@router.get("/profile")
def get_user_profile(current_user: User = Depends(get_current_user)):
    """Get the current user's profile information"""
    user_data = {
        "username": current_user.username,
        "email": current_user.email,
        "company_name": current_user.company_name,
        "mobile_number": current_user.mobile_number,
        "address": current_user.address,
        "state": current_user.state,
        "state_code": current_user.state_code,
        "gstin": current_user.gstin,
        "bank_name": current_user.bank_name,
        "account_number": current_user.account_number,
        "branch": current_user.branch,
        "ifsc_code": current_user.ifsc_code,
    }

    # Add logo and signature URLs if they exist
    if hasattr(current_user, 'logo_path') and current_user.logo_path:
        user_data["logo_url"] = f"/uploads/logos/{os.path.basename(current_user.logo_path)}"

    if hasattr(current_user, 'signature_path') and current_user.signature_path:
        user_data["signature_url"] = f"/uploads/signatures/{os.path.basename(current_user.signature_path)}"

    return user_data

@router.get("/company")
def get_company_info(current_user: User = Depends(get_current_user)):
    """Get the current user's company information"""
    company_data = {
        "company_name": current_user.company_name,
        "address": current_user.address,
        "state": current_user.state,
        "state_code": current_user.state_code,
        "gstin": current_user.gstin,
        "mobile_number": current_user.mobile_number,
        "bank_name": current_user.bank_name,
        "account_number": current_user.account_number,
        "branch": current_user.branch,
        "ifsc_code": current_user.ifsc_code,
    }

    # Add logo and signature URLs if they exist
    if hasattr(current_user, 'logo_path') and current_user.logo_path:
        company_data["logo_url"] = f"/uploads/logos/{os.path.basename(current_user.logo_path)}"

    if hasattr(current_user, 'signature_path') and current_user.signature_path:
        company_data["signature_url"] = f"/uploads/signatures/{os.path.basename(current_user.signature_path)}"

    return company_data

@router.put("/profile")
async def update_user_profile(
    company_name: str = Form(None),
    mobile_number: str = Form(None),
    address: str = Form(None),
    state: str = Form(None),
    state_code: str = Form(None),
    gstin: str = Form(None),
    bank_name: str = Form(None),
    account_number: str = Form(None),
    branch: str = Form(None),
    ifsc_code: str = Form(None),
    logo: Optional[UploadFile] = File(None),
    signature: Optional[UploadFile] = File(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Update the current user's profile information"""
    # Get the user from the database
    user = db.query(User).filter(User.id == current_user.id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Update user fields if provided
    if company_name is not None:
        user.company_name = company_name
    if mobile_number is not None:
        user.mobile_number = mobile_number
    if address is not None:
        user.address = address
    if state is not None:
        user.state = state
    if state_code is not None:
        user.state_code = state_code
    if gstin is not None:
        user.gstin = gstin
    if bank_name is not None:
        user.bank_name = bank_name
    if account_number is not None:
        user.account_number = account_number
    if branch is not None:
        user.branch = branch
    if ifsc_code is not None:
        user.ifsc_code = ifsc_code

    # Handle logo upload
    if logo:
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        logo_filename = f"{user.id}_{timestamp}_{logo.filename}"
        logo_path = os.path.join(LOGO_DIR, logo_filename)

        # Save the file
        with open(logo_path, "wb") as buffer:
            shutil.copyfileobj(logo.file, buffer)

        # Update user's logo path
        user.logo_path = logo_path

    # Handle signature upload
    if signature:
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        signature_filename = f"{user.id}_{timestamp}_{signature.filename}"
        signature_path = os.path.join(SIGNATURE_DIR, signature_filename)

        # Save the file
        with open(signature_path, "wb") as buffer:
            shutil.copyfileobj(signature.file, buffer)

        # Update user's signature path
        user.signature_path = signature_path

    # Recalculate profile completion
    completion_percentage = user.calculate_profile_completion()
    user.profile_completion_percentage = completion_percentage
    user.profile_completed = user.is_profile_complete_for_invoice()

    # Commit changes to the database
    db.commit()

    return {
        "message": "Profile updated successfully",
        "profile_completion_percentage": completion_percentage,
        "profile_completed": user.profile_completed
    }

@router.put("/change-password")
def change_password(
    password_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Change the current user's password"""
    # Verify current password
    if not verify_password(password_data["current_password"], current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )

    # Update password
    user = db.query(User).filter(User.id == current_user.id).first()
    user.hashed_password = get_password_hash(password_data["new_password"])
    db.commit()

    return {"message": "Password updated successfully"}
