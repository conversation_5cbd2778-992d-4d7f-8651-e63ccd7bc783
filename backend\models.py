from sqlalchemy import <PERSON>umn, <PERSON><PERSON><PERSON>, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime, timezone
from sqlalchemy import event

Base = declarative_base()

class InvoiceTemplate(Base):
    __tablename__ = "invoice_templates"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True)
    description = Column(String(255))
    preview_image = Column(String(255))  # Path to the template preview image
    template_file = Column(String(255))  # Path to the template file or identifier
    is_default = Column(Boolean, default=False)
    user_id = Column(Integer, ForeignKey("users.id"))  # Link template to user

    # Template configuration as JSON
    colors = Column(Text)  # JSON string for color configuration
    fonts = Column(Text)   # JSON string for font configuration
    layout = Column(Text)  # JSON string for layout configuration
    fields = Column(Text)  # JSON string for field visibility
    branding = Column(Text)  # JSON string for branding elements

    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # Relationships
    user = relationship("User", back_populates="invoice_templates")

    def __str__(self):
        return f"InvoiceTemplate(id={self.id}, name='{self.name}')"

    def __repr__(self):
        return self.__str__()

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255))
    company_name = Column(String(100), nullable=True)
    mobile_number = Column(String(10), nullable=True)
    address = Column(String(255), nullable=True)
    state = Column(String(50), nullable=True)
    state_code = Column(String(10), nullable=True)
    gstin = Column(String(15), unique=True, nullable=True)
    bank_name = Column(String(100), nullable=True)
    account_number = Column(String(50), nullable=True)
    branch = Column(String(100), nullable=True)
    ifsc_code = Column(String(20), nullable=True)
    logo_path = Column(String(255), nullable=True)
    signature_path = Column(String(255), nullable=True)

    # Profile completion tracking
    profile_completed = Column(Boolean, default=False)
    profile_completion_percentage = Column(Integer, default=0)

    # Subscription related fields
    subscription_plan = Column(String(20), default="free")  # free, annual, three_year
    subscription_status = Column(String(20), default="active")  # active, expired
    subscription_start_date = Column(DateTime, nullable=True)
    subscription_end_date = Column(DateTime, nullable=True)
    invoice_count = Column(Integer, default=0)  # Track invoice usage for free plan
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    customers = relationship("Customer", back_populates="owner")
    products = relationship("Product", back_populates="user")
    invoices = relationship("Invoice", back_populates="user")
    invoice_templates = relationship("InvoiceTemplate", back_populates="user")
    notifications = relationship("Notification", back_populates="recipient")

    def calculate_profile_completion(self):
        """Calculate profile completion percentage"""
        total_fields = 0
        completed_fields = 0

        # Personal Info fields (required for invoice creation)
        personal_fields = [
            self.company_name, self.mobile_number, self.email
        ]
        total_fields += len(personal_fields)
        completed_fields += sum(1 for field in personal_fields if field and field.strip())

        # Company Details fields (required for invoice creation)
        company_fields = [
            self.address, self.state, self.state_code, self.gstin
        ]
        total_fields += len(company_fields)
        completed_fields += sum(1 for field in company_fields if field and field.strip())

        # Bank Details fields (optional)
        bank_fields = [
            self.bank_name, self.account_number, self.branch, self.ifsc_code
        ]
        total_fields += len(bank_fields)
        completed_fields += sum(1 for field in bank_fields if field and field.strip())

        # Branding fields (optional)
        branding_fields = [
            self.logo_path, self.signature_path
        ]
        total_fields += len(branding_fields)
        completed_fields += sum(1 for field in branding_fields if field and field.strip())

        percentage = int((completed_fields / total_fields) * 100) if total_fields > 0 else 0
        return percentage

    def is_profile_complete_for_invoice(self):
        """Check if profile is complete enough to create invoices"""
        # Required fields for invoice creation: Personal Info + Company Details
        required_fields = [
            self.company_name, self.mobile_number, self.email,
            self.address, self.state, self.state_code, self.gstin
        ]
        return all(field and field.strip() for field in required_fields)

    def can_create_invoice(self):
        """Check if user can create more invoices based on their plan and profile completion"""
        # First check if profile is complete enough
        if not self.is_profile_complete_for_invoice():
            return False

        # Then check subscription limits
        if self.subscription_plan == "free":
            return self.invoice_count < 15
        return True  # Paid plans have unlimited invoices

    def is_subscription_active(self):
        """Check if user's subscription is active"""
        if self.subscription_plan == "free":
            return True
        if self.subscription_status != "active":
            return False
        if self.subscription_end_date:
            # Handle both timezone-aware and naive datetime objects
            current_time = datetime.now(timezone.utc)
            end_date = self.subscription_end_date

            # If end_date is naive, make it timezone-aware (assume UTC)
            if end_date.tzinfo is None:
                end_date = end_date.replace(tzinfo=timezone.utc)

            if current_time > end_date:
                return False
        return True

    def get_subscription_info(self):
        """Get subscription information for the user"""
        # Helper function to safely convert datetime to ISO format
        def safe_isoformat(dt):
            if dt is None:
                return None
            # If datetime is naive, assume UTC
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            return dt.isoformat()

        return {
            "plan": self.subscription_plan,
            "status": self.subscription_status,
            "start_date": safe_isoformat(self.subscription_start_date),
            "end_date": safe_isoformat(self.subscription_end_date),
            "invoice_count": self.invoice_count,
            "can_create_invoice": self.can_create_invoice(),
            "is_active": self.is_subscription_active()
        }

class Customer(Base):
    __tablename__ = "customers"
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100))
    address = Column(String(255))
    state = Column(String(50))
    state_code = Column(String(2))
    gstin = Column(String(15))
    mobile_number = Column(String(10))
    user_id = Column(Integer, ForeignKey("users.id"))

    owner = relationship("User", back_populates="customers")
    invoices = relationship("Invoice", back_populates="customer")

    def __str__(self):
        return f"Customer(id={self.id}, name='{self.name}')"

    def __repr__(self):
        return self.__str__()

class Product(Base):
    __tablename__ = "products"
    id = Column(Integer, primary_key=True, index=True)
    description = Column(String(255))
    hsn_code = Column(String(10))
    available_quantity = Column(Integer, default=0)
    uom = Column(String(10))
    rate = Column(Float)

    user_id = Column(Integer, ForeignKey("users.id"))
    user = relationship("User", back_populates="products")

class Invoice(Base):
    __tablename__ = "invoices"
    id = Column(Integer, primary_key=True, index=True)
    invoice_number = Column(String(50), unique=True)
    invoice_date = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    po_number = Column(String(50))
    po_date = Column(DateTime)
    reverse_charge = Column(String(3))
    vehicle_number = Column(String(20))
    transporter_name = Column(String(100))
    taxable_value = Column(Float)
    freight_forwarding = Column(Float, default=0.0)
    sgst = Column(Float)
    cgst = Column(Float)
    igst = Column(Float, default=0.0)
    grand_total = Column(Float)
    amount_in_words = Column(String(255))
    user_id = Column(Integer, ForeignKey("users.id"))
    customer_id = Column(Integer, ForeignKey("customers.id"))
    # Consignee (Ship To) details
    consignee_name = Column(String(100), nullable=True)
    consignee_address = Column(String(255), nullable=True)
    consignee_state = Column(String(50), nullable=True)
    consignee_state_code = Column(String(10), nullable=True)
    consignee_gstin = Column(String(15), nullable=True)

    user = relationship("User", back_populates="invoices")
    customer = relationship("Customer", back_populates="invoices")
    items = relationship("InvoiceItem", back_populates="invoice")

    def __str__(self):
        return f"Invoice(id={self.id}, number='{self.invoice_number}', customer_id={self.customer_id})"

    def __repr__(self):
        return self.__str__()

class InvoiceItem(Base):
    __tablename__ = "invoice_items"
    id = Column(Integer, primary_key=True, index=True)
    sr_no = Column(Integer)
    description = Column(String(255))
    hsn_code = Column(String(10))
    quantity = Column(Float)
    uom = Column(String(10))
    rate = Column(Float)
    discount = Column(Float, default=0.0)  # Added discount column
    sgst_rate = Column(Float, default=0.0)  # Added SGST rate column
    cgst_rate = Column(Float, default=0.0)  # Added CGST rate column
    igst_rate = Column(Float, default=0.0)  # Added IGST rate column
    taxable_value = Column(Float)
    invoice_id = Column(Integer, ForeignKey("invoices.id"))

    invoice = relationship("Invoice", back_populates="items")

    def __str__(self):
        return f"InvoiceItem(id={self.id}, description='{self.description}', quantity={self.quantity}, rate={self.rate})"

    def __repr__(self):
        return self.__str__()

# Event listener function to automatically generate invoice number
def set_invoice_number(_, connection, invoice):  # The first parameter (mapper) is not used but required by SQLAlchemy
    if invoice.invoice_number:
        return  # Skip if invoice number is already set

    # Get the session
    from sqlalchemy.orm import Session
    session = Session(bind=connection)

    # Get the user
    user = session.query(User).filter(User.id == invoice.user_id).first()
    if not user:
        return

    # Get the last invoice for this user
    last_invoice = session.query(Invoice).filter(
        Invoice.user_id == invoice.user_id,
        Invoice.id != invoice.id  # Exclude current invoice
    ).order_by(Invoice.id.desc()).first()

    last_invoice_number = last_invoice.invoice_number if last_invoice else None

    # Generate the invoice number using your existing function
    from utils import generate_invoice_number
    invoice.invoice_number = generate_invoice_number(user, last_invoice_number)

    # Close the session
    session.close()

# Register the event listener
event.listen(Invoice, 'before_insert', set_invoice_number)


class SubscriptionPlan(Base):
    __tablename__ = "subscription_plans"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), unique=True)  # free, annual, three_year
    display_name = Column(String(100))  # Free, Annual, 3-Year Plan
    price = Column(Float)  # Price in INR
    duration_months = Column(Integer)  # Duration in months (0 for free, 12 for annual, 36 for 3-year)
    invoice_limit = Column(Integer, nullable=True)  # NULL for unlimited, 15 for free
    features = Column(Text)  # JSON string of features
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)

    def __str__(self):
        return f"SubscriptionPlan(id={self.id}, name='{self.name}', price={self.price})"


class Payment(Base):
    __tablename__ = "payments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    subscription_plan_id = Column(Integer, ForeignKey("subscription_plans.id"))
    amount = Column(Float)
    currency = Column(String(3), default="INR")
    payment_method = Column(String(50))  # razorpay, stripe, bank_transfer, etc.
    payment_gateway_id = Column(String(100))  # Payment gateway transaction ID
    payment_status = Column(String(20), default="pending")  # pending, completed, failed, refunded
    payment_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # Relationships
    user = relationship("User")
    subscription_plan = relationship("SubscriptionPlan")

    def __str__(self):
        return f"Payment(id={self.id}, user_id={self.user_id}, amount={self.amount}, status='{self.payment_status}')"


class SubscriptionHistory(Base):
    __tablename__ = "subscription_history"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    subscription_plan_id = Column(Integer, ForeignKey("subscription_plans.id"))
    payment_id = Column(Integer, ForeignKey("payments.id"), nullable=True)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    status = Column(String(20))  # active, expired
    created_at = Column(DateTime, default=datetime.now)

    # Relationships
    user = relationship("User")
    subscription_plan = relationship("SubscriptionPlan")
    payment = relationship("Payment")

    def __str__(self):
        return f"SubscriptionHistory(id={self.id}, user_id={self.user_id}, plan_id={self.subscription_plan_id}, status='{self.status}')"


class Admin(Base):
    """Separate Admin model for system administrators (not customers)"""
    __tablename__ = "admins"

    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(100), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(255))
    full_name = Column(String(100))
    role = Column(String(50), default="admin")  # admin, super_admin, manager
    is_active = Column(Boolean, default=True)
    last_login = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_by = Column(Integer, ForeignKey("admins.id"), nullable=True)  # Which admin created this admin

    # Relationships
    sent_notifications = relationship("Notification", back_populates="sender")


class Notification(Base):
    """Notification system for admin-to-customer messaging"""
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    type = Column(String(20), nullable=False)  # 'custom' or 'automatic'
    message_type = Column(String(50), nullable=False)  # 'subscription_activated', 'subscription_expired', 'subscription_reminder', 'custom'
    sender_id = Column(Integer, ForeignKey("admins.id"), nullable=True)  # NULL for automatic messages
    recipient_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.now)
    read_at = Column(DateTime, nullable=True)

    # Relationships
    sender = relationship("Admin", back_populates="sent_notifications")
    recipient = relationship("User", back_populates="notifications")


class NotificationTemplate(Base):
    """Templates for automatic notifications"""
    __tablename__ = "notification_templates"

    id = Column(Integer, primary_key=True, index=True)
    message_type = Column(String(50), unique=True, nullable=False)
    title_template = Column(String(255), nullable=False)
    message_template = Column(Text, nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    def __str__(self):
        return f"NotificationTemplate(id={self.id}, message_type='{self.message_type}')"

    def __repr__(self):
        return self.__str__()


