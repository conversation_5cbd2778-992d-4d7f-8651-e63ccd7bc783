{"ast": null, "code": "var _jsxFileName = \"D:\\\\invoice_project\\\\gst-billing-software\\\\frontend\\\\src\\\\context\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport jwt_decode from 'jwt-decode';\nimport { useNavigate } from 'react-router-dom';\nimport { API_BASE_URL } from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport function AuthProvider({\n  children\n}) {\n  _s();\n  const [currentUser, setCurrentUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const navigate = useNavigate();\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    const storedUserData = localStorage.getItem('userData');\n    console.log('Initial load - Token exists:', !!token);\n    console.log('Initial load - Stored user data exists:', !!storedUserData);\n    if (token) {\n      try {\n        // Check if token is expired\n        const decoded = jwt_decode(token);\n        const currentTime = Date.now() / 1000;\n        if (decoded.exp && decoded.exp < currentTime) {\n          // Token is expired\n          console.log('Token expired, logging out');\n          logout();\n        } else {\n          // Try to use stored user data first for more complete information\n          let userData;\n          if (storedUserData) {\n            try {\n              userData = JSON.parse(storedUserData);\n              console.log('Using stored user data:', userData);\n            } catch (e) {\n              console.error('Error parsing stored user data:', e);\n            }\n          }\n\n          // If no stored data or parsing failed, use decoded token\n          if (!userData) {\n            userData = {\n              ...decoded,\n              token: token,\n              company_name: decoded.company_name || '',\n              username: decoded.sub || '',\n              email: decoded.email || ''\n            };\n            console.log('Using decoded token data:', userData);\n          }\n\n          // Set currentUser with all available data\n          setCurrentUser(userData);\n          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n        }\n      } catch (error) {\n        console.error('Invalid token:', error);\n        logout();\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async (username, password) => {\n    try {\n      // Create form data instead of JSON\n      const formData = new URLSearchParams();\n      formData.append('username', username);\n      formData.append('password', password);\n      console.log('Attempting login for:', username);\n      const response = await axios.post(`${API_BASE_URL}/token`, formData, {\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        }\n      });\n      console.log('Login response:', response.data);\n      const token = response.data.access_token;\n      localStorage.setItem('token', token);\n\n      // Decode the JWT token\n      const decoded = jwt_decode(token);\n      console.log('Decoded token:', decoded);\n\n      // Get user info from response if available\n      const userInfo = response.data.user_info || {};\n      console.log('User info from response:', userInfo);\n\n      // Set currentUser to include both decoded data, token, and user info\n      const userData = {\n        ...decoded,\n        token: token,\n        // Add the token to the user object\n        // Prioritize user_info from response, then fall back to decoded token data\n        company_name: userInfo.company_name || decoded.company_name || '',\n        username: userInfo.username || decoded.sub || username,\n        email: userInfo.email || decoded.email || ''\n      };\n      console.log('Setting current user data:', userData);\n      setCurrentUser(userData);\n\n      // Store the complete user data in localStorage for persistence\n      localStorage.setItem('userData', JSON.stringify(userData));\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      navigate('/dashboard');\n      return true;\n    } catch (error) {\n      var _error$response;\n      console.error('Login error:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n      throw error;\n    }\n  };\n  const register = async userData => {\n    try {\n      console.log('Registering new user:', userData.email);\n      console.log('Company name being registered:', userData.company_name);\n      const response = await axios.post(`${API_BASE_URL}/register`, userData);\n      console.log('Registration successful, response:', response.data);\n\n      // Only attempt login if registration was successful\n      console.log('Attempting login after registration');\n      return await login(userData.email, userData.password); // Use email as username\n    } catch (error) {\n      var _error$response2;\n      console.error('Registration error:', ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.data) || error.message);\n      throw error;\n    }\n  };\n  const logout = () => {\n    console.log('Logging out, clearing user data');\n    localStorage.removeItem('token');\n    localStorage.removeItem('userData');\n    setCurrentUser(null);\n    delete axios.defaults.headers.common['Authorization'];\n    navigate('/login');\n  };\n  const value = {\n    currentUser,\n    login,\n    register,\n    logout\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: !loading && children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 5\n  }, this);\n}\n_s(AuthProvider, \"4XW06Xv3Wo+INaDFXZmN4cjHv/s=\", false, function () {\n  return [useNavigate];\n});\n_c = AuthProvider;\nexport function useAuth() {\n  _s2();\n  return useContext(AuthContext);\n}\n_s2(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "jwt_decode", "useNavigate", "API_BASE_URL", "jsxDEV", "_jsxDEV", "AuthContext", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "currentUser", "setCurrentUser", "loading", "setLoading", "navigate", "token", "localStorage", "getItem", "storedUserData", "console", "log", "decoded", "currentTime", "Date", "now", "exp", "logout", "userData", "JSON", "parse", "e", "error", "company_name", "username", "sub", "email", "defaults", "headers", "common", "login", "password", "formData", "URLSearchParams", "append", "response", "post", "data", "access_token", "setItem", "userInfo", "user_info", "stringify", "_error$response", "message", "register", "_error$response2", "removeItem", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useAuth", "_s2", "$RefreshReg$"], "sources": ["D:/invoice_project/gst-billing-software/frontend/src/context/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport jwt_decode from 'jwt-decode';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { API_BASE_URL } from '../config';\r\n\r\nconst AuthContext = createContext();\r\n\r\nexport function AuthProvider({ children }) {\r\n  const [currentUser, setCurrentUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const navigate = useNavigate();\r\n\r\n  useEffect(() => {\r\n    const token = localStorage.getItem('token');\r\n    const storedUserData = localStorage.getItem('userData');\r\n\r\n    console.log('Initial load - Token exists:', !!token);\r\n    console.log('Initial load - Stored user data exists:', !!storedUserData);\r\n\r\n    if (token) {\r\n      try {\r\n        // Check if token is expired\r\n        const decoded = jwt_decode(token);\r\n        const currentTime = Date.now() / 1000;\r\n\r\n        if (decoded.exp && decoded.exp < currentTime) {\r\n          // Token is expired\r\n          console.log('Token expired, logging out');\r\n          logout();\r\n        } else {\r\n          // Try to use stored user data first for more complete information\r\n          let userData;\r\n\r\n          if (storedUserData) {\r\n            try {\r\n              userData = JSON.parse(storedUserData);\r\n              console.log('Using stored user data:', userData);\r\n            } catch (e) {\r\n              console.error('Error parsing stored user data:', e);\r\n            }\r\n          }\r\n\r\n          // If no stored data or parsing failed, use decoded token\r\n          if (!userData) {\r\n            userData = {\r\n              ...decoded,\r\n              token: token,\r\n              company_name: decoded.company_name || '',\r\n              username: decoded.sub || '',\r\n              email: decoded.email || ''\r\n            };\r\n            console.log('Using decoded token data:', userData);\r\n          }\r\n\r\n          // Set currentUser with all available data\r\n          setCurrentUser(userData);\r\n          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n        }\r\n      } catch (error) {\r\n        console.error('Invalid token:', error);\r\n        logout();\r\n      }\r\n    }\r\n    setLoading(false);\r\n  }, []);\r\n\r\n  const login = async (username, password) => {\r\n    try {\r\n      // Create form data instead of JSON\r\n      const formData = new URLSearchParams();\r\n      formData.append('username', username);\r\n      formData.append('password', password);\r\n\r\n      console.log('Attempting login for:', username);\r\n\r\n      const response = await axios.post(`${API_BASE_URL}/token`, formData, {\r\n        headers: {\r\n          'Content-Type': 'application/x-www-form-urlencoded',\r\n        },\r\n      });\r\n\r\n      console.log('Login response:', response.data);\r\n\r\n      const token = response.data.access_token;\r\n      localStorage.setItem('token', token);\r\n\r\n      // Decode the JWT token\r\n      const decoded = jwt_decode(token);\r\n      console.log('Decoded token:', decoded);\r\n\r\n      // Get user info from response if available\r\n      const userInfo = response.data.user_info || {};\r\n      console.log('User info from response:', userInfo);\r\n\r\n      // Set currentUser to include both decoded data, token, and user info\r\n      const userData = {\r\n        ...decoded,\r\n        token: token,  // Add the token to the user object\r\n        // Prioritize user_info from response, then fall back to decoded token data\r\n        company_name: userInfo.company_name || decoded.company_name || '',\r\n        username: userInfo.username || decoded.sub || username,\r\n        email: userInfo.email || decoded.email || ''\r\n      };\r\n\r\n      console.log('Setting current user data:', userData);\r\n      setCurrentUser(userData);\r\n\r\n      // Store the complete user data in localStorage for persistence\r\n      localStorage.setItem('userData', JSON.stringify(userData));\r\n\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n      navigate('/dashboard');\r\n      return true;\r\n    } catch (error) {\r\n      console.error('Login error:', error.response?.data || error.message);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const register = async (userData) => {\r\n    try {\r\n      console.log('Registering new user:', userData.email);\r\n      console.log('Company name being registered:', userData.company_name);\r\n\r\n      const response = await axios.post(`${API_BASE_URL}/register`, userData);\r\n      console.log('Registration successful, response:', response.data);\r\n\r\n      // Only attempt login if registration was successful\r\n      console.log('Attempting login after registration');\r\n      return await login(userData.email, userData.password); // Use email as username\r\n    } catch (error) {\r\n      console.error('Registration error:', error.response?.data || error.message);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const logout = () => {\r\n    console.log('Logging out, clearing user data');\r\n    localStorage.removeItem('token');\r\n    localStorage.removeItem('userData');\r\n    setCurrentUser(null);\r\n    delete axios.defaults.headers.common['Authorization'];\r\n    navigate('/login');\r\n  };\r\n\r\n  const value = {\r\n    currentUser,\r\n    login,\r\n    register,\r\n    logout,\r\n  };\r\n\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {!loading && children}\r\n    </AuthContext.Provider>\r\n  );\r\n}\r\n\r\nexport function useAuth() {\r\n  return useContext(AuthContext);\r\n}\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,YAAY,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,WAAW,gBAAGV,aAAa,CAAC,CAAC;AAEnC,OAAO,SAASW,YAAYA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EACzC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAMgB,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9BH,SAAS,CAAC,MAAM;IACd,MAAMgB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMC,cAAc,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEvDE,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,CAAC,CAACL,KAAK,CAAC;IACpDI,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE,CAAC,CAACF,cAAc,CAAC;IAExE,IAAIH,KAAK,EAAE;MACT,IAAI;QACF;QACA,MAAMM,OAAO,GAAGpB,UAAU,CAACc,KAAK,CAAC;QACjC,MAAMO,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI;QAErC,IAAIH,OAAO,CAACI,GAAG,IAAIJ,OAAO,CAACI,GAAG,GAAGH,WAAW,EAAE;UAC5C;UACAH,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;UACzCM,MAAM,CAAC,CAAC;QACV,CAAC,MAAM;UACL;UACA,IAAIC,QAAQ;UAEZ,IAAIT,cAAc,EAAE;YAClB,IAAI;cACFS,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACX,cAAc,CAAC;cACrCC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,QAAQ,CAAC;YAClD,CAAC,CAAC,OAAOG,CAAC,EAAE;cACVX,OAAO,CAACY,KAAK,CAAC,iCAAiC,EAAED,CAAC,CAAC;YACrD;UACF;;UAEA;UACA,IAAI,CAACH,QAAQ,EAAE;YACbA,QAAQ,GAAG;cACT,GAAGN,OAAO;cACVN,KAAK,EAAEA,KAAK;cACZiB,YAAY,EAAEX,OAAO,CAACW,YAAY,IAAI,EAAE;cACxCC,QAAQ,EAAEZ,OAAO,CAACa,GAAG,IAAI,EAAE;cAC3BC,KAAK,EAAEd,OAAO,CAACc,KAAK,IAAI;YAC1B,CAAC;YACDhB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEO,QAAQ,CAAC;UACpD;;UAEA;UACAhB,cAAc,CAACgB,QAAQ,CAAC;UACxB3B,KAAK,CAACoC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUvB,KAAK,EAAE;QACpE;MACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;QACdZ,OAAO,CAACY,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtCL,MAAM,CAAC,CAAC;MACV;IACF;IACAb,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM0B,KAAK,GAAG,MAAAA,CAAON,QAAQ,EAAEO,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMC,QAAQ,GAAG,IAAIC,eAAe,CAAC,CAAC;MACtCD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEV,QAAQ,CAAC;MACrCQ,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;MAErCrB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEa,QAAQ,CAAC;MAE9C,MAAMW,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,IAAI,CAAC,GAAG1C,YAAY,QAAQ,EAAEsC,QAAQ,EAAE;QACnEJ,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEFlB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEwB,QAAQ,CAACE,IAAI,CAAC;MAE7C,MAAM/B,KAAK,GAAG6B,QAAQ,CAACE,IAAI,CAACC,YAAY;MACxC/B,YAAY,CAACgC,OAAO,CAAC,OAAO,EAAEjC,KAAK,CAAC;;MAEpC;MACA,MAAMM,OAAO,GAAGpB,UAAU,CAACc,KAAK,CAAC;MACjCI,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,OAAO,CAAC;;MAEtC;MACA,MAAM4B,QAAQ,GAAGL,QAAQ,CAACE,IAAI,CAACI,SAAS,IAAI,CAAC,CAAC;MAC9C/B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6B,QAAQ,CAAC;;MAEjD;MACA,MAAMtB,QAAQ,GAAG;QACf,GAAGN,OAAO;QACVN,KAAK,EAAEA,KAAK;QAAG;QACf;QACAiB,YAAY,EAAEiB,QAAQ,CAACjB,YAAY,IAAIX,OAAO,CAACW,YAAY,IAAI,EAAE;QACjEC,QAAQ,EAAEgB,QAAQ,CAAChB,QAAQ,IAAIZ,OAAO,CAACa,GAAG,IAAID,QAAQ;QACtDE,KAAK,EAAEc,QAAQ,CAACd,KAAK,IAAId,OAAO,CAACc,KAAK,IAAI;MAC5C,CAAC;MAEDhB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,QAAQ,CAAC;MACnDhB,cAAc,CAACgB,QAAQ,CAAC;;MAExB;MACAX,YAAY,CAACgC,OAAO,CAAC,UAAU,EAAEpB,IAAI,CAACuB,SAAS,CAACxB,QAAQ,CAAC,CAAC;MAE1D3B,KAAK,CAACoC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUvB,KAAK,EAAE;MAClED,QAAQ,CAAC,YAAY,CAAC;MACtB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOiB,KAAK,EAAE;MAAA,IAAAqB,eAAA;MACdjC,OAAO,CAACY,KAAK,CAAC,cAAc,EAAE,EAAAqB,eAAA,GAAArB,KAAK,CAACa,QAAQ,cAAAQ,eAAA,uBAAdA,eAAA,CAAgBN,IAAI,KAAIf,KAAK,CAACsB,OAAO,CAAC;MACpE,MAAMtB,KAAK;IACb;EACF,CAAC;EAED,MAAMuB,QAAQ,GAAG,MAAO3B,QAAQ,IAAK;IACnC,IAAI;MACFR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEO,QAAQ,CAACQ,KAAK,CAAC;MACpDhB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEO,QAAQ,CAACK,YAAY,CAAC;MAEpE,MAAMY,QAAQ,GAAG,MAAM5C,KAAK,CAAC6C,IAAI,CAAC,GAAG1C,YAAY,WAAW,EAAEwB,QAAQ,CAAC;MACvER,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEwB,QAAQ,CAACE,IAAI,CAAC;;MAEhE;MACA3B,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO,MAAMmB,KAAK,CAACZ,QAAQ,CAACQ,KAAK,EAAER,QAAQ,CAACa,QAAQ,CAAC,CAAC,CAAC;IACzD,CAAC,CAAC,OAAOT,KAAK,EAAE;MAAA,IAAAwB,gBAAA;MACdpC,OAAO,CAACY,KAAK,CAAC,qBAAqB,EAAE,EAAAwB,gBAAA,GAAAxB,KAAK,CAACa,QAAQ,cAAAW,gBAAA,uBAAdA,gBAAA,CAAgBT,IAAI,KAAIf,KAAK,CAACsB,OAAO,CAAC;MAC3E,MAAMtB,KAAK;IACb;EACF,CAAC;EAED,MAAML,MAAM,GAAGA,CAAA,KAAM;IACnBP,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9CJ,YAAY,CAACwC,UAAU,CAAC,OAAO,CAAC;IAChCxC,YAAY,CAACwC,UAAU,CAAC,UAAU,CAAC;IACnC7C,cAAc,CAAC,IAAI,CAAC;IACpB,OAAOX,KAAK,CAACoC,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrDxB,QAAQ,CAAC,QAAQ,CAAC;EACpB,CAAC;EAED,MAAM2C,KAAK,GAAG;IACZ/C,WAAW;IACX6B,KAAK;IACLe,QAAQ;IACR5B;EACF,CAAC;EAED,oBACErB,OAAA,CAACC,WAAW,CAACoD,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAjD,QAAA,EAChC,CAACI,OAAO,IAAIJ;EAAQ;IAAAmD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAE3B;AAACrD,EAAA,CAtJeF,YAAY;EAAA,QAGTL,WAAW;AAAA;AAAA6D,EAAA,GAHdxD,YAAY;AAwJ5B,OAAO,SAASyD,OAAOA,CAAA,EAAG;EAAAC,GAAA;EACxB,OAAOpE,UAAU,CAACS,WAAW,CAAC;AAChC;AAAC2D,GAAA,CAFeD,OAAO;AAAA,IAAAD,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}