import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, Container, Row, Col } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import ProductBranding from '../../components/common/ProductBranding';
import '../../styles/auth.css';



export default function Register() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    company_name: '',
    mobile_number: ''
  });
  const [loading, setLoading] = useState(false);
  const { register } = useAuth();
  const { showError, showSuccess } = useToast();
  const navigate = useNavigate();

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const validateEmail = (email) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const validateMobileNumber = (mobile) => {
    return /^\d{10}$/.test(mobile);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!validateEmail(formData.email)) {
      return showError('Please enter a valid email address');
    }

    if (!validateMobileNumber(formData.mobile_number)) {
      return showError('Mobile number must be exactly 10 digits');
    }

    if (formData.password.length < 6) {
      return showError('Password should be at least 6 characters');
    }

    if (formData.password !== formData.confirmPassword) {
      return showError('Passwords do not match');
    }

    if (!formData.company_name.trim()) {
      return showError('Company name is required');
    }

    try {
      setLoading(true);

      // Send the registration data directly
      await register(formData);
      showSuccess('Account created successfully! Welcome to SwiftBiller.');
      navigate('/dashboard');
    } catch (err) {
      console.error('Registration error:', err);

      // Handle different types of errors
      if (err.response) {
        const errorData = err.response.data;

        // Handle validation errors (422 status)
        if (err.response.status === 422 && errorData.detail) {
          if (Array.isArray(errorData.detail)) {
            // Pydantic validation errors
            const errorMessages = errorData.detail.map(error => {
              const field = error.loc ? error.loc[error.loc.length - 1] : 'field';
              return `${field}: ${error.msg}`;
            }).join(', ');
            showError(`Validation error: ${errorMessages}`);
          } else {
            showError(`Validation error: ${errorData.detail}`);
          }
        } else if (errorData.detail) {
          // Other API errors
          showError(errorData.detail);
        } else {
          showError(`Server error (${err.response.status}): Please try again`);
        }
      } else if (err.request) {
        showError('No response from server. Please check your connection and try again.');
      } else {
        showError(`Error: ${err.message}`);
      }
    }
    setLoading(false);
  };

  return (
    <div className="auth-container">
      <Container>
        <Row className="justify-content-center">
          <Col xs={12} lg={10} xl={9}>
            <Card className="auth-card registration-card">
              <Card.Header className="auth-header">
                <div className="auth-logo">
                  <ProductBranding
                    variant="centered"
                    logoSize="large"
                  />
                </div>
                <h2 className="auth-title">Create Account</h2>
                <p className="auth-subtitle">Join our platform to streamline your business</p>
              </Card.Header>

              <Card.Body className="auth-body">
                <Form onSubmit={handleSubmit} className="registration-form">
                  <div className="registration-section">
                    <h5 className="section-title"><i className="fas fa-user-circle me-2"></i>Account Information</h5>
                    <Form.Group className="mb-3">
                      <Form.Label className="auth-form-label">
                        <i className="fas fa-envelope"></i>Email Address
                      </Form.Label>
                      <Form.Control
                        type="email"
                        name="email"
                        required
                        value={formData.email}
                        onChange={handleChange}
                        className="auth-input"
                        placeholder="Enter your email address"
                      />
                      <small className="text-muted">This will be used as your username</small>
                    </Form.Group>

                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label className="auth-form-label">
                            <i className="fas fa-lock"></i>Password
                          </Form.Label>
                          <Form.Control
                            type="password"
                            name="password"
                            required
                            value={formData.password}
                            onChange={handleChange}
                            className="auth-input"
                            placeholder="Create a password"
                          />
                          <small className="text-muted">Must be at least 6 characters</small>
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label className="auth-form-label">
                            <i className="fas fa-check-circle"></i>Confirm Password
                          </Form.Label>
                          <Form.Control
                            type="password"
                            name="confirmPassword"
                            required
                            value={formData.confirmPassword}
                            onChange={handleChange}
                            className="auth-input"
                            placeholder="Confirm your password"
                          />
                        </Form.Group>
                      </Col>
                    </Row>
                  </div>

                  <div className="registration-section">
                    <h5 className="section-title"><i className="fas fa-building me-2"></i>Basic Information</h5>
                    <Row>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label className="auth-form-label">
                            <i className="fas fa-briefcase"></i>Company Name
                          </Form.Label>
                          <Form.Control
                            type="text"
                            name="company_name"
                            required
                            value={formData.company_name}
                            onChange={handleChange}
                            className="auth-input"
                            placeholder="Enter your company name"
                          />
                        </Form.Group>
                      </Col>
                      <Col md={6}>
                        <Form.Group className="mb-3">
                          <Form.Label className="auth-form-label">
                            <i className="fas fa-mobile-alt"></i>Mobile Number
                          </Form.Label>
                          <Form.Control
                            type="tel"
                            name="mobile_number"
                            required
                            value={formData.mobile_number}
                            onChange={handleChange}
                            className="auth-input"
                            placeholder="Enter 10-digit mobile number"
                            maxLength="10"
                          />
                        </Form.Group>
                      </Col>
                    </Row>

                    <div className="alert alert-info">
                      <i className="fas fa-info-circle me-2"></i>
                      <strong>Complete your profile after registration</strong><br />
                      You'll need to add company details (address, state, GSTIN) and bank information before creating invoices.
                    </div>
                  </div>

                  <div className="terms-section mb-4">
                    <Form.Check
                      type="checkbox"
                      id="terms-checkbox"
                      label={<span>I agree to the <Link to="#" className="auth-link">Terms of Service</Link> and <Link to="#" className="auth-link">Privacy Policy</Link></span>}
                      className="auth-checkbox"
                      required
                    />
                  </div>

                  <Button
                    disabled={loading}
                    className="auth-btn w-100 mt-3"
                    type="submit"
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Creating Account...
                      </>
                    ) : (
                      <>Create Account <i className="fas fa-arrow-right ms-2"></i></>
                    )}
                  </Button>
                </Form>

                <div className="auth-footer">
                  Already have an account? <Link to="/login" className="auth-link">Sign In</Link>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
