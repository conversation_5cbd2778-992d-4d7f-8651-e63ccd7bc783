{"ast": null, "code": "var _jsxFileName = \"D:\\\\invoice_project\\\\gst-billing-software\\\\frontend\\\\src\\\\components\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport ProductBranding from './common/ProductBranding';\nimport axios from 'axios';\nimport '../styles/sidebar.css';\n\n// Import icons if FontAwesome is available\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nlet FontAwesomeIcon;\nlet faTachometerAlt, faUsers, faBoxes, faFileInvoice, faChartBar, faUser, faSignOutAlt, faCog, faTools, faFileAlt, faCrown, faBell;\ntry {\n  FontAwesomeIcon = require('@fortawesome/react-fontawesome').FontAwesomeIcon;\n  const icons = require('@fortawesome/free-solid-svg-icons');\n  faTachometerAlt = icons.faTachometerAlt;\n  faUsers = icons.faUsers;\n  faBoxes = icons.faBoxes;\n  faFileInvoice = icons.faFileInvoice;\n  faChartBar = icons.faChartBar;\n  faUser = icons.faUser;\n  faSignOutAlt = icons.faSignOutAlt;\n  faCog = icons.faCog;\n  faTools = icons.faTools;\n  faFileAlt = icons.faFileAlt;\n  faCrown = icons.faCrown;\n  faBell = icons.faBell;\n} catch (error) {\n  console.error('Error loading FontAwesome:', error);\n}\nexport default function Sidebar({\n  onLogout\n}) {\n  _s();\n  var _userData$company_nam, _currentUser$company_, _currentUser$username, _currentUser$email;\n  const {\n    currentUser\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState({\n    company_name: '',\n    logo_url: '',\n    profile_completion_percentage: 0,\n    profile_completed: false\n  });\n  const [logoError, setLogoError] = useState(false);\n\n  // For debugging\n  React.useEffect(() => {\n    console.log(\"Sidebar - Current user data:\", currentUser);\n  }, [currentUser]);\n\n  // Fetch user profile data\n  useEffect(() => {\n    if (currentUser !== null && currentUser !== void 0 && currentUser.token) {\n      fetchUserProfile();\n    }\n  }, [currentUser]);\n  const fetchUserProfile = async () => {\n    if (!(currentUser !== null && currentUser !== void 0 && currentUser.token)) {\n      console.log('No authentication token available');\n      return;\n    }\n    try {\n      const response = await axios.get('http://localhost:8000/profile', {\n        headers: {\n          Authorization: `Bearer ${currentUser.token}`\n        }\n      });\n      console.log('Sidebar - Profile API response:', response.data);\n      console.log('Sidebar - Logo URL:', response.data.logo_url);\n      setUserData(response.data);\n      setLogoError(false); // Reset logo error when new data is fetched\n    } catch (error) {\n      console.error('Error fetching profile in sidebar:', error);\n    }\n  };\n\n  // Handle logo click to navigate to profile settings\n  const handleLogoClick = () => {\n    navigate('/dashboard?section=profile');\n  };\n\n  // Get company name with fallbacks\n  const getCompanyName = () => {\n    if (currentUser !== null && currentUser !== void 0 && currentUser.company_name) {\n      return currentUser.company_name;\n    }\n    if (currentUser !== null && currentUser !== void 0 && currentUser.username) {\n      return currentUser.username;\n    }\n    if (currentUser !== null && currentUser !== void 0 && currentUser.email) {\n      return currentUser.email;\n    }\n    return \"User\";\n  };\n\n  // Function to scroll to top when clicking on sidebar links\n  const handleLinkClick = () => {\n    // Force scroll to top with multiple approaches\n    window.scrollTo(0, 0);\n    window.scrollTo({\n      top: 0,\n      left: 0,\n      behavior: 'auto'\n    });\n    document.documentElement.scrollTop = 0;\n    document.body.scrollTop = 0;\n\n    // Also set a timeout to scroll again after navigation\n    setTimeout(() => {\n      window.scrollTo(0, 0);\n      document.documentElement.scrollTop = 0;\n      document.body.scrollTop = 0;\n    }, 100);\n  };\n\n  // Check if the current path matches the link\n  const isActive = path => {\n    if (path === '/dashboard') {\n      // Dashboard is active only if the path is /dashboard and there's no section parameter\n      return location.pathname === path && !location.search.includes('section=');\n    }\n    return location.pathname === path;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: /*#__PURE__*/_jsxDEV(ProductBranding, {\n        variant: \"sidebar\",\n        logoSize: \"xlarge\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-menu\",\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/dashboard\",\n        className: `sidebar-item ${isActive('/dashboard') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faTachometerAlt ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faTachometerAlt,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDCCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/customers\",\n        className: `sidebar-item ${isActive('/customers') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faUsers ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faUsers,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDC65\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Customers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/products\",\n        className: `sidebar-item ${isActive('/products') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faBoxes ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faBoxes,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDCE6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Products\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/invoices\",\n        className: `sidebar-item ${isActive('/invoices') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faFileInvoice ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faFileInvoice,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDCC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Invoices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/reports\",\n        className: `sidebar-item ${isActive('/reports') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faChartBar ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faChartBar,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDCC8\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Reports\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/dashboard?section=invoice-template\",\n        className: `sidebar-item ${location.search.includes('section=invoice-template') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faFileAlt ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faFileAlt,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDCC4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Invoice Templates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/dashboard?section=business-tools\",\n        className: `sidebar-item ${location.search.includes('section=business-tools') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faTools ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faTools,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDD27\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Business Tools\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/dashboard?section=profile\",\n        className: `sidebar-item ${location.search.includes('section=profile') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faCog ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faCog,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\u2699\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Profile Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/dashboard?section=notifications\",\n        className: `sidebar-item ${location.search.includes('section=notifications') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faBell ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faBell,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDD14\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/subscription\",\n        className: `sidebar-item ${isActive('/subscription') ? 'active' : ''}`,\n        onClick: handleLinkClick,\n        children: [FontAwesomeIcon && faCrown ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faCrown,\n          className: \"sidebar-icon\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"sidebar-icon\",\n          children: \"\\uD83D\\uDC51\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Subscription\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-user\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `user-avatar ${userData !== null && userData !== void 0 && userData.logo_url ? 'has-logo' : 'has-letter'}`,\n          onClick: handleLogoClick,\n          style: {\n            cursor: 'pointer'\n          },\n          title: \"Click to go to Profile Settings\",\n          children: userData !== null && userData !== void 0 && userData.logo_url && !logoError ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `http://localhost:8000${userData.logo_url}`,\n            alt: \"Company Logo\",\n            className: \"sidebar-avatar-logo\",\n            onError: e => {\n              console.error('Logo failed to load:', userData.logo_url);\n              setLogoError(true);\n            },\n            onLoad: () => {\n              console.log('Logo loaded successfully:', userData.logo_url);\n              setLogoError(false);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"sidebar-avatar-letter\",\n            children: (userData === null || userData === void 0 ? void 0 : (_userData$company_nam = userData.company_name) === null || _userData$company_nam === void 0 ? void 0 : _userData$company_nam.charAt(0)) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$company_ = currentUser.company_name) === null || _currentUser$company_ === void 0 ? void 0 : _currentUser$company_.charAt(0)) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$username = currentUser.username) === null || _currentUser$username === void 0 ? void 0 : _currentUser$username.charAt(0)) || (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$email = currentUser.email) === null || _currentUser$email === void 0 ? void 0 : _currentUser$email.charAt(0)) || 'U'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-info\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-name\",\n            children: getCompanyName()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"logout-button\",\n        onClick: onLogout,\n        children: [FontAwesomeIcon && faSignOutAlt ? /*#__PURE__*/_jsxDEV(FontAwesomeIcon, {\n          icon: faSignOutAlt,\n          className: \"me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"me-2\",\n          children: \"\\uD83D\\uDEAA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this), \"Logout\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 121,\n    columnNumber: 5\n  }, this);\n}\n_s(Sidebar, \"4xSwJpvuSTPeSAKKVFf1qoyVWWw=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "useNavigate", "useAuth", "ProductBranding", "axios", "jsxDEV", "_jsxDEV", "FontAwesomeIcon", "faTachometerAlt", "faUsers", "faBoxes", "faFileInvoice", "faChartBar", "faUser", "faSignOutAlt", "faCog", "faTools", "faFileAlt", "faCrown", "faBell", "require", "icons", "error", "console", "Sidebar", "onLogout", "_s", "_userData$company_nam", "_currentUser$company_", "_currentUser$username", "_currentUser$email", "currentUser", "location", "navigate", "userData", "setUserData", "company_name", "logo_url", "profile_completion_percentage", "profile_completed", "logoError", "setLogoError", "log", "token", "fetchUserProfile", "response", "get", "headers", "Authorization", "data", "handleLogoClick", "getCompanyName", "username", "email", "handleLinkClick", "window", "scrollTo", "top", "left", "behavior", "document", "documentElement", "scrollTop", "body", "setTimeout", "isActive", "path", "pathname", "search", "includes", "className", "children", "variant", "logoSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "onClick", "icon", "style", "cursor", "title", "src", "alt", "onError", "e", "onLoad", "char<PERSON>t", "_c", "$RefreshReg$"], "sources": ["D:/invoice_project/gst-billing-software/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../context/AuthContext';\nimport ProductBranding from './common/ProductBranding';\nimport axios from 'axios';\nimport '../styles/sidebar.css';\n\n// Import icons if FontAwesome is available\nlet FontAwesomeIcon;\nlet faTachometerAlt, faUsers, faBoxes, faFileInvoice, faChartBar, faUser, faSignOutAlt, faCog, faTools, faFileAlt, faCrown, faBell;\ntry {\n  FontAwesomeIcon = require('@fortawesome/react-fontawesome').FontAwesomeIcon;\n  const icons = require('@fortawesome/free-solid-svg-icons');\n  faTachometerAlt = icons.faTachometerAlt;\n  faUsers = icons.faUsers;\n  faBoxes = icons.faBoxes;\n  faFileInvoice = icons.faFileInvoice;\n  faChartBar = icons.faChartBar;\n  faUser = icons.faUser;\n  faSignOutAlt = icons.faSignOutAlt;\n  faCog = icons.faCog;\n  faTools = icons.faTools;\n  faFileAlt = icons.faFileAlt;\n  faCrown = icons.faCrown;\n  faBell = icons.faBell;\n} catch (error) {\n  console.error('Error loading FontAwesome:', error);\n}\n\nexport default function Sidebar({ onLogout }) {\n  const { currentUser } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState({\n    company_name: '',\n    logo_url: '',\n    profile_completion_percentage: 0,\n    profile_completed: false\n  });\n  const [logoError, setLogoError] = useState(false);\n\n  // For debugging\n  React.useEffect(() => {\n    console.log(\"Sidebar - Current user data:\", currentUser);\n  }, [currentUser]);\n\n  // Fetch user profile data\n  useEffect(() => {\n    if (currentUser?.token) {\n      fetchUserProfile();\n    }\n  }, [currentUser]);\n\n  const fetchUserProfile = async () => {\n    if (!currentUser?.token) {\n      console.log('No authentication token available');\n      return;\n    }\n\n    try {\n      const response = await axios.get('http://localhost:8000/profile', {\n        headers: { Authorization: `Bearer ${currentUser.token}` }\n      });\n      console.log('Sidebar - Profile API response:', response.data);\n      console.log('Sidebar - Logo URL:', response.data.logo_url);\n      setUserData(response.data);\n      setLogoError(false); // Reset logo error when new data is fetched\n    } catch (error) {\n      console.error('Error fetching profile in sidebar:', error);\n    }\n  };\n\n  // Handle logo click to navigate to profile settings\n  const handleLogoClick = () => {\n    navigate('/dashboard?section=profile');\n  };\n\n  // Get company name with fallbacks\n  const getCompanyName = () => {\n    if (currentUser?.company_name) {\n      return currentUser.company_name;\n    }\n\n    if (currentUser?.username) {\n      return currentUser.username;\n    }\n\n    if (currentUser?.email) {\n      return currentUser.email;\n    }\n\n    return \"User\";\n  };\n\n  // Function to scroll to top when clicking on sidebar links\n  const handleLinkClick = () => {\n    // Force scroll to top with multiple approaches\n    window.scrollTo(0, 0);\n    window.scrollTo({ top: 0, left: 0, behavior: 'auto' });\n    document.documentElement.scrollTop = 0;\n    document.body.scrollTop = 0;\n\n    // Also set a timeout to scroll again after navigation\n    setTimeout(() => {\n      window.scrollTo(0, 0);\n      document.documentElement.scrollTop = 0;\n      document.body.scrollTop = 0;\n    }, 100);\n  };\n\n  // Check if the current path matches the link\n  const isActive = (path) => {\n    if (path === '/dashboard') {\n      // Dashboard is active only if the path is /dashboard and there's no section parameter\n      return location.pathname === path && !location.search.includes('section=');\n    }\n    return location.pathname === path;\n  };\n\n  return (\n    <div className=\"sidebar\">\n      <div className=\"sidebar-header\">\n        <ProductBranding\n          variant=\"sidebar\"\n          logoSize=\"xlarge\"\n        />\n      </div>\n\n      <div className=\"sidebar-menu\">\n        <Link to=\"/dashboard\" className={`sidebar-item ${isActive('/dashboard') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faTachometerAlt ? (\n            <FontAwesomeIcon icon={faTachometerAlt} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">📊</span>\n          )}\n          <span>Dashboard</span>\n        </Link>\n\n        <Link to=\"/customers\" className={`sidebar-item ${isActive('/customers') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faUsers ? (\n            <FontAwesomeIcon icon={faUsers} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">👥</span>\n          )}\n          <span>Customers</span>\n        </Link>\n\n        <Link to=\"/products\" className={`sidebar-item ${isActive('/products') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faBoxes ? (\n            <FontAwesomeIcon icon={faBoxes} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">📦</span>\n          )}\n          <span>Products</span>\n        </Link>\n\n        <Link to=\"/invoices\" className={`sidebar-item ${isActive('/invoices') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faFileInvoice ? (\n            <FontAwesomeIcon icon={faFileInvoice} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">📄</span>\n          )}\n          <span>Invoices</span>\n        </Link>\n\n        <Link to=\"/reports\" className={`sidebar-item ${isActive('/reports') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faChartBar ? (\n            <FontAwesomeIcon icon={faChartBar} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">📈</span>\n          )}\n          <span>Reports</span>\n        </Link>\n\n        <Link to=\"/dashboard?section=invoice-template\" className={`sidebar-item ${location.search.includes('section=invoice-template') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faFileAlt ? (\n            <FontAwesomeIcon icon={faFileAlt} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">📄</span>\n          )}\n          <span>Invoice Templates</span>\n        </Link>\n\n        <Link to=\"/dashboard?section=business-tools\" className={`sidebar-item ${location.search.includes('section=business-tools') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faTools ? (\n            <FontAwesomeIcon icon={faTools} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">🔧</span>\n          )}\n          <span>Business Tools</span>\n        </Link>\n\n        <Link to=\"/dashboard?section=profile\" className={`sidebar-item ${location.search.includes('section=profile') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faCog ? (\n            <FontAwesomeIcon icon={faCog} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">⚙️</span>\n          )}\n          <span>Profile Settings</span>\n        </Link>\n\n        <Link to=\"/dashboard?section=notifications\" className={`sidebar-item ${location.search.includes('section=notifications') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faBell ? (\n            <FontAwesomeIcon icon={faBell} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">🔔</span>\n          )}\n          <span>Notifications</span>\n        </Link>\n\n        <Link to=\"/subscription\" className={`sidebar-item ${isActive('/subscription') ? 'active' : ''}`} onClick={handleLinkClick}>\n          {FontAwesomeIcon && faCrown ? (\n            <FontAwesomeIcon icon={faCrown} className=\"sidebar-icon\" />\n          ) : (\n            <span className=\"sidebar-icon\">👑</span>\n          )}\n          <span>Subscription</span>\n        </Link>\n      </div>\n\n      <div className=\"sidebar-footer\">\n        <div className=\"sidebar-user\">\n          <div\n            className={`user-avatar ${userData?.logo_url ? 'has-logo' : 'has-letter'}`}\n            onClick={handleLogoClick}\n            style={{ cursor: 'pointer' }}\n            title=\"Click to go to Profile Settings\"\n          >\n            {userData?.logo_url && !logoError ? (\n              <img\n                src={`http://localhost:8000${userData.logo_url}`}\n                alt=\"Company Logo\"\n                className=\"sidebar-avatar-logo\"\n                onError={(e) => {\n                  console.error('Logo failed to load:', userData.logo_url);\n                  setLogoError(true);\n                }}\n                onLoad={() => {\n                  console.log('Logo loaded successfully:', userData.logo_url);\n                  setLogoError(false);\n                }}\n              />\n            ) : (\n              <span className=\"sidebar-avatar-letter\">\n                {userData?.company_name?.charAt(0) || currentUser?.company_name?.charAt(0) || currentUser?.username?.charAt(0) || currentUser?.email?.charAt(0) || 'U'}\n              </span>\n            )}\n          </div>\n          <div className=\"user-info\">\n            <div className=\"user-name\">{getCompanyName()}</div>\n          </div>\n        </div>\n\n        <button className=\"logout-button\" onClick={onLogout}>\n          {FontAwesomeIcon && faSignOutAlt ? (\n            <FontAwesomeIcon icon={faSignOutAlt} className=\"me-2\" />\n          ) : (\n            <span className=\"me-2\">🚪</span>\n          )}\n          Logout\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,IAAIC,eAAe;AACnB,IAAIC,eAAe,EAAEC,OAAO,EAAEC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,MAAM,EAAEC,YAAY,EAAEC,KAAK,EAAEC,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM;AAClI,IAAI;EACFZ,eAAe,GAAGa,OAAO,CAAC,gCAAgC,CAAC,CAACb,eAAe;EAC3E,MAAMc,KAAK,GAAGD,OAAO,CAAC,mCAAmC,CAAC;EAC1DZ,eAAe,GAAGa,KAAK,CAACb,eAAe;EACvCC,OAAO,GAAGY,KAAK,CAACZ,OAAO;EACvBC,OAAO,GAAGW,KAAK,CAACX,OAAO;EACvBC,aAAa,GAAGU,KAAK,CAACV,aAAa;EACnCC,UAAU,GAAGS,KAAK,CAACT,UAAU;EAC7BC,MAAM,GAAGQ,KAAK,CAACR,MAAM;EACrBC,YAAY,GAAGO,KAAK,CAACP,YAAY;EACjCC,KAAK,GAAGM,KAAK,CAACN,KAAK;EACnBC,OAAO,GAAGK,KAAK,CAACL,OAAO;EACvBC,SAAS,GAAGI,KAAK,CAACJ,SAAS;EAC3BC,OAAO,GAAGG,KAAK,CAACH,OAAO;EACvBC,MAAM,GAAGE,KAAK,CAACF,MAAM;AACvB,CAAC,CAAC,OAAOG,KAAK,EAAE;EACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;AACpD;AAEA,eAAe,SAASE,OAAOA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,kBAAA;EAC5C,MAAM;IAAEC;EAAY,CAAC,GAAG7B,OAAO,CAAC,CAAC;EACjC,MAAM8B,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAMiC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAC;IACvCuC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,6BAA6B,EAAE,CAAC;IAChCC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACAD,KAAK,CAACE,SAAS,CAAC,MAAM;IACpByB,OAAO,CAACmB,GAAG,CAAC,8BAA8B,EAAEX,WAAW,CAAC;EAC1D,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;;EAEjB;EACAjC,SAAS,CAAC,MAAM;IACd,IAAIiC,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEY,KAAK,EAAE;MACtBC,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACb,WAAW,CAAC,CAAC;EAEjB,MAAMa,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,EAACb,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEY,KAAK,GAAE;MACvBpB,OAAO,CAACmB,GAAG,CAAC,mCAAmC,CAAC;MAChD;IACF;IAEA,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMzC,KAAK,CAAC0C,GAAG,CAAC,+BAA+B,EAAE;QAChEC,OAAO,EAAE;UAAEC,aAAa,EAAE,UAAUjB,WAAW,CAACY,KAAK;QAAG;MAC1D,CAAC,CAAC;MACFpB,OAAO,CAACmB,GAAG,CAAC,iCAAiC,EAAEG,QAAQ,CAACI,IAAI,CAAC;MAC7D1B,OAAO,CAACmB,GAAG,CAAC,qBAAqB,EAAEG,QAAQ,CAACI,IAAI,CAACZ,QAAQ,CAAC;MAC1DF,WAAW,CAACU,QAAQ,CAACI,IAAI,CAAC;MAC1BR,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;IACvB,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D;EACF,CAAC;;EAED;EACA,MAAM4B,eAAe,GAAGA,CAAA,KAAM;IAC5BjB,QAAQ,CAAC,4BAA4B,CAAC;EACxC,CAAC;;EAED;EACA,MAAMkB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIpB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEK,YAAY,EAAE;MAC7B,OAAOL,WAAW,CAACK,YAAY;IACjC;IAEA,IAAIL,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEqB,QAAQ,EAAE;MACzB,OAAOrB,WAAW,CAACqB,QAAQ;IAC7B;IAEA,IAAIrB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEsB,KAAK,EAAE;MACtB,OAAOtB,WAAW,CAACsB,KAAK;IAC1B;IAEA,OAAO,MAAM;EACf,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACAC,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrBD,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAO,CAAC,CAAC;IACtDC,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAG,CAAC;IACtCF,QAAQ,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;;IAE3B;IACAE,UAAU,CAAC,MAAM;MACfT,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrBI,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAG,CAAC;MACtCF,QAAQ,CAACG,IAAI,CAACD,SAAS,GAAG,CAAC;IAC7B,CAAC,EAAE,GAAG,CAAC;EACT,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAIC,IAAI,IAAK;IACzB,IAAIA,IAAI,KAAK,YAAY,EAAE;MACzB;MACA,OAAOlC,QAAQ,CAACmC,QAAQ,KAAKD,IAAI,IAAI,CAAClC,QAAQ,CAACoC,MAAM,CAACC,QAAQ,CAAC,UAAU,CAAC;IAC5E;IACA,OAAOrC,QAAQ,CAACmC,QAAQ,KAAKD,IAAI;EACnC,CAAC;EAED,oBACE5D,OAAA;IAAKgE,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBjE,OAAA;MAAKgE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BjE,OAAA,CAACH,eAAe;QACdqE,OAAO,EAAC,SAAS;QACjBC,QAAQ,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENvE,OAAA;MAAKgE,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BjE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,YAAY;QAACR,SAAS,EAAE,gBAAgBL,QAAQ,CAAC,YAAY,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACc,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GACjHhE,eAAe,IAAIC,eAAe,gBACjCF,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAExE,eAAgB;UAAC8D,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEnEvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,YAAY;QAACR,SAAS,EAAE,gBAAgBL,QAAQ,CAAC,YAAY,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACc,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GACjHhE,eAAe,IAAIE,OAAO,gBACzBH,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAEvE,OAAQ;UAAC6D,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3DvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,WAAW;QAACR,SAAS,EAAE,gBAAgBL,QAAQ,CAAC,WAAW,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACc,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GAC/GhE,eAAe,IAAIG,OAAO,gBACzBJ,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAEtE,OAAQ;UAAC4D,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3DvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,WAAW;QAACR,SAAS,EAAE,gBAAgBL,QAAQ,CAAC,WAAW,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACc,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GAC/GhE,eAAe,IAAII,aAAa,gBAC/BL,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAErE,aAAc;UAAC2D,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEjEvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,UAAU;QAACR,SAAS,EAAE,gBAAgBL,QAAQ,CAAC,UAAU,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACc,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GAC7GhE,eAAe,IAAIK,UAAU,gBAC5BN,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAEpE,UAAW;UAAC0D,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE9DvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,qCAAqC;QAACR,SAAS,EAAE,gBAAgBtC,QAAQ,CAACoC,MAAM,CAACC,QAAQ,CAAC,0BAA0B,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACU,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GACxKhE,eAAe,IAAIU,SAAS,gBAC3BX,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAE/D,SAAU;UAACqD,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE7DvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,mCAAmC;QAACR,SAAS,EAAE,gBAAgBtC,QAAQ,CAACoC,MAAM,CAACC,QAAQ,CAAC,wBAAwB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACU,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GACpKhE,eAAe,IAAIS,OAAO,gBACzBV,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAEhE,OAAQ;UAACsD,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3DvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAc;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,4BAA4B;QAACR,SAAS,EAAE,gBAAgBtC,QAAQ,CAACoC,MAAM,CAACC,QAAQ,CAAC,iBAAiB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACU,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GACtJhE,eAAe,IAAIQ,KAAK,gBACvBT,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAEjE,KAAM;UAACuD,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEzDvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAgB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,kCAAkC;QAACR,SAAS,EAAE,gBAAgBtC,QAAQ,CAACoC,MAAM,CAACC,QAAQ,CAAC,uBAAuB,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACU,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GAClKhE,eAAe,IAAIY,MAAM,gBACxBb,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAE7D,MAAO;UAACmD,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE1DvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAa;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eAEPvE,OAAA,CAACP,IAAI;QAAC+E,EAAE,EAAC,eAAe;QAACR,SAAS,EAAE,gBAAgBL,QAAQ,CAAC,eAAe,CAAC,GAAG,QAAQ,GAAG,EAAE,EAAG;QAACc,OAAO,EAAEzB,eAAgB;QAAAiB,QAAA,GACvHhE,eAAe,IAAIW,OAAO,gBACzBZ,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAE9D,OAAQ;UAACoD,SAAS,EAAC;QAAc;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAE3DvE,OAAA;UAAMgE,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxC,eACDvE,OAAA;UAAAiE,QAAA,EAAM;QAAY;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENvE,OAAA;MAAKgE,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BjE,OAAA;QAAKgE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjE,OAAA;UACEgE,SAAS,EAAE,eAAepC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEG,QAAQ,GAAG,UAAU,GAAG,YAAY,EAAG;UAC3E0C,OAAO,EAAE7B,eAAgB;UACzB+B,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAU,CAAE;UAC7BC,KAAK,EAAC,iCAAiC;UAAAZ,QAAA,EAEtCrC,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEG,QAAQ,IAAI,CAACG,SAAS,gBAC/BlC,OAAA;YACE8E,GAAG,EAAE,wBAAwBlD,QAAQ,CAACG,QAAQ,EAAG;YACjDgD,GAAG,EAAC,cAAc;YAClBf,SAAS,EAAC,qBAAqB;YAC/BgB,OAAO,EAAGC,CAAC,IAAK;cACdhE,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEY,QAAQ,CAACG,QAAQ,CAAC;cACxDI,YAAY,CAAC,IAAI,CAAC;YACpB,CAAE;YACF+C,MAAM,EAAEA,CAAA,KAAM;cACZjE,OAAO,CAACmB,GAAG,CAAC,2BAA2B,EAAER,QAAQ,CAACG,QAAQ,CAAC;cAC3DI,YAAY,CAAC,KAAK,CAAC;YACrB;UAAE;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAEFvE,OAAA;YAAMgE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EACpC,CAAArC,QAAQ,aAARA,QAAQ,wBAAAP,qBAAA,GAARO,QAAQ,CAAEE,YAAY,cAAAT,qBAAA,uBAAtBA,qBAAA,CAAwB8D,MAAM,CAAC,CAAC,CAAC,MAAI1D,WAAW,aAAXA,WAAW,wBAAAH,qBAAA,GAAXG,WAAW,CAAEK,YAAY,cAAAR,qBAAA,uBAAzBA,qBAAA,CAA2B6D,MAAM,CAAC,CAAC,CAAC,MAAI1D,WAAW,aAAXA,WAAW,wBAAAF,qBAAA,GAAXE,WAAW,CAAEqB,QAAQ,cAAAvB,qBAAA,uBAArBA,qBAAA,CAAuB4D,MAAM,CAAC,CAAC,CAAC,MAAI1D,WAAW,aAAXA,WAAW,wBAAAD,kBAAA,GAAXC,WAAW,CAAEsB,KAAK,cAAAvB,kBAAA,uBAAlBA,kBAAA,CAAoB2D,MAAM,CAAC,CAAC,CAAC,KAAI;UAAG;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClJ;QACP;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNvE,OAAA;UAAKgE,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBjE,OAAA;YAAKgE,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEpB,cAAc,CAAC;UAAC;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENvE,OAAA;QAAQgE,SAAS,EAAC,eAAe;QAACS,OAAO,EAAEtD,QAAS;QAAA8C,QAAA,GACjDhE,eAAe,IAAIO,YAAY,gBAC9BR,OAAA,CAACC,eAAe;UAACyE,IAAI,EAAElE,YAAa;UAACwD,SAAS,EAAC;QAAM;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAExDvE,OAAA;UAAMgE,SAAS,EAAC,MAAM;UAAAC,QAAA,EAAC;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAChC,EAAC,QAEJ;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnD,EAAA,CA3OuBF,OAAO;EAAA,QACLtB,OAAO,EACdF,WAAW,EACXC,WAAW;AAAA;AAAAyF,EAAA,GAHNlE,OAAO;AAAA,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}