import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, Card, Container, Row, Col } from 'react-bootstrap';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import { useNavigate, Link } from 'react-router-dom';
import ProductBranding from '../../components/common/ProductBranding';
import '../../styles/auth.css';

export default function Login() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const { showError, showSuccess } = useToast();
  const navigate = useNavigate();

  async function handleSubmit(e) {
    e.preventDefault();
    try {
      setLoading(true);
      await login(username, password);
      showSuccess('Login successful! Welcome back.', { duration: 3000 });
      navigate('/dashboard');
    } catch (err) {
      showError('Failed to log in: ' + (err.response?.data?.detail || err.message));
    }
    setLoading(false);
  }

  return (
    <div className="auth-container">
      <Container>
        <Row className="justify-content-center">
          <Col xs={12} md={10} lg={8} xl={6}>
            <Card className="auth-card">
              <Card.Header className="auth-header">
                <div className="auth-logo">
                  <ProductBranding
                    variant="centered"
                    logoSize="large"
                  />
                </div>
                <h2 className="auth-title">Welcome Back</h2>
                <p className="auth-subtitle">Sign in to your account</p>
              </Card.Header>

              <Card.Body className="auth-body">
                <Form onSubmit={handleSubmit}>
                  <Form.Group id="username" className="mb-4">
                    <Form.Label className="auth-form-label">
                      <i className="fas fa-envelope"></i>Email Address
                    </Form.Label>
                    <Form.Control
                      type="email"
                      required
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="auth-input"
                      placeholder="Enter your email address"
                    />
                  </Form.Group>

                  <Form.Group id="password" className="mb-4">
                    <Form.Label className="auth-form-label">
                      <i className="fas fa-lock"></i>Password
                    </Form.Label>
                    <Form.Control
                      type="password"
                      required
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="auth-input"
                      placeholder="Enter your password"
                    />
                  </Form.Group>

                  <div className="d-flex justify-content-between align-items-center mb-4">
                    <Form.Check
                      type="checkbox"
                      id="remember-me"
                      label="Remember me"
                      className="auth-checkbox"
                    />
                    <Link to="#" className="auth-link">Forgot password?</Link>
                  </div>

                  <Button
                    disabled={loading}
                    className="auth-btn w-100"
                    type="submit"
                  >
                    {loading ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        Signing In...
                      </>
                    ) : (
                      <>Sign In <i className="fas fa-arrow-right ms-2"></i></>
                    )}
                  </Button>
                </Form>

                <div className="auth-divider">
                  <span className="auth-divider-text">OR</span>
                </div>

                <div className="auth-footer">
                  Don't have an account? <Link to="/register" className="auth-link">Create Account</Link>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  );
}
