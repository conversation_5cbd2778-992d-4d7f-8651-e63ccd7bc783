import React, { useState, useEffect } from 'react';
import { Form, Button, Card, Row, Col, Table, Modal, Alert } from 'react-bootstrap';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import { useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import '../../styles/invoices.css';

export default function CreateInvoice() {
  const [customers, setCustomers] = useState([]);
  const [products, setProducts] = useState([]);
  const [companyInfo, setCompanyInfo] = useState(null);
  const [profileCompletion, setProfileCompletion] = useState(null);
  // const [invoiceNumber, setInvoiceNumber] = useState(''); // Removed unused state

  const [formData, setFormData] = useState({
    customer_id: '',
    invoice_date: new Date().toISOString().split('T')[0], // Set default to today
    po_number: '',
    po_date: '',
    reverse_charge: 'No',
    freight_forwarding: 0,
    vehicle_number: '',
    transporter_name: '',
    // Consignee (Ship To) details
    consignee_name: '',
    consignee_address: '',
    consignee_state: '',
    consignee_state_code: '',
    consignee_gstin: '',
    items: []
  });

  // State to track if consignee is same as buyer
  const [sameAsBuyer, setSameAsBuyer] = useState(false);
  const [currentItem, setCurrentItem] = useState({
    sr_no: 1,
    description: '',
    hsn_code: '',
    quantity: 1,
    uom: 'NOS',
    rate: 0,
    discount: 0, // Explicitly set to 0 (number)
    sgst_rate: 0,
    cgst_rate: 0,
    igst_rate: 0,
    product_id: null,
    available_quantity: 0,
    taxable_value: 0, // Initialize taxable_value to 0
    sgst_amount: 0, // Tax amount for SGST
    cgst_amount: 0, // Tax amount for CGST
    igst_amount: 0  // Tax amount for IGST
  });
  const [loading, setLoading] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [newProductAdded, setNewProductAdded] = useState('');
  const { currentUser } = useAuth();
  const { showError, showSuccess, showWarning } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    if (currentUser) {
      fetchCustomers();
      fetchProducts();
      fetchCompanyInfo();
      generateInvoiceNumber();
      fetchProfileCompletion();
    }
  }, [currentUser]);

  const fetchProfileCompletion = async () => {
    try {
      const response = await axios.get('http://localhost:8000/profile/completion');
      setProfileCompletion(response.data);
    } catch (error) {
      console.error('Error fetching profile completion:', error);
    }
  };

  // Effect to handle GST rates based on state codes
  useEffect(() => {
    if (formData.customer_id && companyInfo) {
      const selectedCustomer = customers.find(c => c.id === parseInt(formData.customer_id));
      if (selectedCustomer) {
        // If state codes match, enable SGST+CGST and disable IGST
        // If state codes don't match, enable IGST and disable SGST+CGST
        const isSameState = selectedCustomer.state_code === companyInfo.state_code;

        // Update current item to reset tax rates based on state
        setCurrentItem(prev => ({
          ...prev,
          sgst_rate: isSameState ? prev.sgst_rate : 0,
          cgst_rate: isSameState ? prev.cgst_rate : 0,
          igst_rate: !isSameState ? prev.igst_rate : 0
        }));
      }
    }
  }, [formData.customer_id, companyInfo, customers]);

  const fetchCompanyInfo = async () => {
    try {
      const response = await axios.get('http://localhost:8000/company'); // Remove trailing slash
      setCompanyInfo(response.data);
    } catch (error) {
      console.error('Error fetching company info:', error);
    }
  };

  const generateInvoiceNumber = async () => {
    try {
      // Get the latest invoice number
      const response = await axios.get('http://localhost:8000/invoices/latest-number/');

      // Generate company prefix
      let prefix = '';
      if (companyInfo && companyInfo.name) {
        // Extract first letter of each word
        prefix = companyInfo.name
          .split(' ')
          .map(word => word.charAt(0).toUpperCase())
          .join('');
      }

      // Determine financial year
      const today = new Date();
      let financialYear;
      if (today.getMonth() >= 3) { // April onwards
        financialYear = `${today.getFullYear()}-${today.getFullYear() + 1 - 2000}`;
      } else {
        financialYear = `${today.getFullYear() - 1}-${today.getFullYear() - 2000}`;
      }

      // Get the serial number and increment it
      let serialNumber = 1;
      if (response.data && response.data.latest_number) {
        serialNumber = parseInt(response.data.latest_number) + 1;
      }

      // Format the invoice number
      const formattedSerialNumber = serialNumber.toString().padStart(2, '0');
      const invoiceNumber = `${prefix}/${financialYear}/${formattedSerialNumber}`;

      // Add the invoice number to the form data
      setFormData(prev => ({
        ...prev,
        invoice_number: invoiceNumber
      }));

    } catch (error) {
      console.error('Error generating invoice number:', error);
    }
  };


  const fetchCustomers = async () => {
    try {
      const response = await axios.get('http://localhost:8000/customers/');
      setCustomers(response.data);
    } catch (error) {
      console.error('Error fetching customers:', error);
    }
  };

  const fetchProducts = async () => {
    try {
      const response = await axios.get('http://localhost:8000/products/');
      setProducts(response.data);
    } catch (error) {
      console.error('Error fetching products:', error);
    }
  };


  const handleChange = (e) => {
    const { name, value } = e.target;

    // Handle customer selection for buyer
    if (name === 'customer_id') {
      const customerId = parseInt(value, 10) || '';
      const selectedCustomer = customers.find(c => c.id === customerId);

      setFormData(prev => ({
        ...prev,
        customer_id: customerId,
        // If "Same as Buyer" is checked, update consignee details too
        ...(sameAsBuyer && selectedCustomer ? {
          consignee_name: selectedCustomer.name,
          consignee_address: selectedCustomer.address,
          consignee_state: selectedCustomer.state,
          consignee_state_code: selectedCustomer.state_code,
          consignee_gstin: selectedCustomer.gstin
        } : {})
      }));
      return;
    }

    // Handle consignee customer selection
    if (name === 'consignee_customer_id') {
      const consigneeId = parseInt(value, 10) || '';
      const selectedConsignee = customers.find(c => c.id === consigneeId);

      if (selectedConsignee) {
        setFormData(prev => ({
          ...prev,
          consignee_name: selectedConsignee.name,
          consignee_state: selectedConsignee.state,
          consignee_state_code: selectedConsignee.state_code,
          consignee_gstin: selectedConsignee.gstin,
          // Note: We don't auto-fill address as it needs to be manually entered
        }));
      }
      return;
    }

    // Handle all other fields
    setFormData(prev => ({
      ...prev,
      [name]: name === 'freight_forwarding' ? parseFloat(value) || 0 : value
    }));
  };

  // Handle "Same as Buyer" checkbox
  const handleSameAsBuyer = (e) => {
    const isChecked = e.target.checked;
    setSameAsBuyer(isChecked);

    if (isChecked) {
      // Get the selected customer
      const selectedCustomer = customers.find(c => c.id === formData.customer_id);

      if (selectedCustomer) {
        setFormData(prev => ({
          ...prev,
          consignee_name: selectedCustomer.name,
          consignee_address: selectedCustomer.address,
          consignee_state: selectedCustomer.state,
          consignee_state_code: selectedCustomer.state_code,
          consignee_gstin: selectedCustomer.gstin
        }));
      }
    } else {
      // Clear consignee fields if unchecked
      setFormData(prev => ({
        ...prev,
        consignee_name: '',
        consignee_address: '',
        consignee_state: '',
        consignee_state_code: '',
        consignee_gstin: ''
      }));
    }
  };


  const handleItemChange = (e) => {
    const { name, value } = e.target;

    if (name === 'quantity') {
      const newQuantity = parseFloat(value) || 0;

      // Check if the quantity exceeds available quantity
      if (newQuantity > currentItem.available_quantity) {
        showError(`Only ${currentItem.available_quantity} units available for ${currentItem.description}`);
        return;
      }
    }

    setCurrentItem(prev => {
      // Handle numeric fields with proper parsing and default to 0 if invalid
      let newValue;
      if (name === 'quantity' || name === 'rate' || name === 'sgst_rate' || name === 'cgst_rate' || name === 'igst_rate') {
        newValue = parseFloat(value) || 0;
      } else if (name === 'discount') {
        newValue = value === '' ? 0 : parseFloat(value) || 0;
      } else {
        newValue = value;
      }

      const newItem = {
        ...prev,
        [name]: newValue
      };

      // Calculate taxable value if quantity, rate, or discount changes
      if (name === 'quantity' || name === 'rate' || name === 'discount') {
        // Ensure discount is treated as a number
        const discount = parseFloat(newItem.discount) || 0;
        const discountedRate = newItem.rate * (1 - discount/100);
        newItem.taxable_value = newItem.quantity * discountedRate;
      }

      // Calculate tax amounts if taxable value or tax rates change
      if (name === 'quantity' || name === 'rate' || name === 'discount' ||
          name === 'sgst_rate' || name === 'cgst_rate' || name === 'igst_rate') {
        // Calculate tax amounts based on taxable value
        newItem.sgst_amount = (newItem.taxable_value * newItem.sgst_rate) / 100;
        newItem.cgst_amount = (newItem.taxable_value * newItem.cgst_rate) / 100;
        newItem.igst_amount = (newItem.taxable_value * newItem.igst_rate) / 100;
      }

      return newItem;
    });
  };

  const handleProductSelect = (product) => {
    // Check if product is already in the invoice items
    const existingItemIndex = formData.items.findIndex(item => item.description === product.description);

    // Calculate available quantity after accounting for items already in the invoice
    let adjustedAvailableQuantity = product.available_quantity;
    if (existingItemIndex !== -1) {
      adjustedAvailableQuantity += formData.items[existingItemIndex].quantity;
    }

    // Don't allow selecting products with no available quantity
    if (adjustedAvailableQuantity <= 0) {
      showError(`Product ${product.description} is out of stock`);
      return;
    }

    // No need to check state codes here as GST rates are initialized to 0

    setCurrentItem({
      sr_no: formData.items.length + 1,
      description: product.description,
      hsn_code: product.hsn_code,
      quantity: 1,
      uom: product.uom,
      rate: product.rate,
      discount: 0, // Explicitly set to 0 (number)
      product_id: product.id,
      available_quantity: adjustedAvailableQuantity, // Store the available quantity for validation
      // Initialize GST rates to 0 - user must select them manually
      sgst_rate: 0,
      cgst_rate: 0,
      igst_rate: 0,
      taxable_value: product.rate, // Initialize taxable_value to rate (since quantity is 1 and discount is 0)
      sgst_amount: 0, // Initialize tax amounts to 0
      cgst_amount: 0,
      igst_amount: 0
    });


  };

  // Search functionality removed as requested


  const addItem = async () => {
    if (!currentItem.description || !currentItem.hsn_code || currentItem.rate <= 0) {
      showError('Please fill all item fields and ensure rate is positive');
      return;
    }

    if (currentItem.quantity <= 0) {
      showError('Quantity must be greater than zero');
      return;
    }

    // Check if the product already exists in the database
    let productExists = false;
    let productId = currentItem.product_id;

    // If no product_id, check if a product with the same description exists
    if (!productId) {
      const existingProduct = products.find(p =>
        p.description.toLowerCase() === currentItem.description.toLowerCase()
      );

      if (existingProduct) {
        productExists = true;
        productId = existingProduct.id;
        // Update current item with the existing product details
        setCurrentItem(prev => ({
          ...prev,
          product_id: existingProduct.id,
          hsn_code: existingProduct.hsn_code,
          uom: existingProduct.uom,
          rate: existingProduct.rate,
          available_quantity: existingProduct.available_quantity
        }));
      } else {
        // Create a new product with available_quantity = 0
        try {
          const newProduct = {
            description: currentItem.description,
            hsn_code: currentItem.hsn_code,
            available_quantity: 0, // Set to 0 as requested
            uom: currentItem.uom,
            rate: currentItem.rate
          };

          const response = await axios.post('http://localhost:8000/products/', newProduct);
          productId = response.data.id;

          // Add the new product to the products array
          setProducts(prev => [...prev, response.data]);

          // Set the product_id in the current item
          setCurrentItem(prev => ({
            ...prev,
            product_id: productId
          }));

          // Show success message
          setNewProductAdded(currentItem.description);
          setShowSuccessModal(true);

        } catch (error) {
          console.error('Error creating product:', error);
          showError('Failed to create new product. Please try again.');
          return;
        }
      }
    }

    // Check if the product is already in the invoice
    const existingItemIndex = formData.items.findIndex(item => item.description === currentItem.description);

    if (existingItemIndex !== -1) {
      // Update existing item instead of adding a new one
      const updatedItems = [...formData.items];
      const existingItem = updatedItems[existingItemIndex];

      // Only check available quantity if it's an existing product with inventory
      if (productExists && currentItem.available_quantity > 0) {
        // Check if the combined quantity exceeds available quantity
        const totalQuantity = existingItem.quantity + currentItem.quantity;
        if (totalQuantity > currentItem.available_quantity) {
          showError(`Cannot add ${currentItem.quantity} more units. Only ${currentItem.available_quantity - existingItem.quantity} units available.`);
          return;
        }
      }

      // Update the existing item
      // Ensure all values are properly parsed as numbers
      const rate = parseFloat(existingItem.rate) || 0;
      const discount = parseFloat(existingItem.discount) || 0;
      const existingQuantity = parseFloat(existingItem.quantity) || 0;
      const additionalQuantity = parseFloat(currentItem.quantity) || 0;
      const newQuantity = existingQuantity + additionalQuantity;

      // Calculate taxable value with discount
      const discountedRate = rate * (1 - discount/100);
      const newTaxableValue = newQuantity * discountedRate;

      // Ensure tax rates are properly parsed as numbers
      const sgstRate = parseFloat(existingItem.sgst_rate) || 0;
      const cgstRate = parseFloat(existingItem.cgst_rate) || 0;
      const igstRate = parseFloat(existingItem.igst_rate) || 0;

      // Calculate tax amounts
      const sgstAmount = (newTaxableValue * sgstRate) / 100;
      const cgstAmount = (newTaxableValue * cgstRate) / 100;
      const igstAmount = (newTaxableValue * igstRate) / 100;

      updatedItems[existingItemIndex] = {
        ...existingItem,
        rate: rate,
        discount: discount,
        quantity: newQuantity,
        taxable_value: newTaxableValue,
        sgst_rate: sgstRate,
        cgst_rate: cgstRate,
        igst_rate: igstRate,
        sgst_amount: sgstAmount,
        cgst_amount: cgstAmount,
        igst_amount: igstAmount
      };

      setFormData(prev => ({
        ...prev,
        items: updatedItems
      }));
    } else {
      // Add as a new item
      // Ensure all values are properly parsed as numbers
      const quantity = parseFloat(currentItem.quantity) || 0;
      const rate = parseFloat(currentItem.rate) || 0;
      const discount = parseFloat(currentItem.discount) || 0;

      // Calculate taxable value with discount
      const discountedRate = rate * (1 - discount/100);
      const taxableValue = quantity * discountedRate;

      // Ensure tax rates are properly parsed as numbers
      const sgstRate = parseFloat(currentItem.sgst_rate) || 0;
      const cgstRate = parseFloat(currentItem.cgst_rate) || 0;
      const igstRate = parseFloat(currentItem.igst_rate) || 0;

      // Calculate tax amounts
      const sgstAmount = (taxableValue * sgstRate) / 100;
      const cgstAmount = (taxableValue * cgstRate) / 100;
      const igstAmount = (taxableValue * igstRate) / 100;

      const newItem = {
        ...currentItem,
        quantity: quantity,
        rate: rate,
        discount: discount,
        taxable_value: taxableValue,
        product_id: productId,
        sgst_rate: sgstRate,
        cgst_rate: cgstRate,
        igst_rate: igstRate,
        sgst_amount: sgstAmount,
        cgst_amount: cgstAmount,
        igst_amount: igstAmount
      };

      setFormData(prev => ({
        ...prev,
        items: [...prev.items, newItem]
      }));
    }

    // Update the products array to reflect the reduced quantity (only for existing products)
    if (productExists) {
      const updatedProducts = products.map(product => {
        if (product.id === productId) {
          return {
            ...product,
            available_quantity: product.available_quantity - currentItem.quantity
          };
        }
        return product;
      });

      setProducts(updatedProducts);
    }

    // Reset current item
    setCurrentItem({
      sr_no: formData.items.length + 2,
      description: '',
      hsn_code: '',
      quantity: 1,
      uom: 'NOS',
      rate: 0,
      discount: 0, // Explicitly set to 0 (number)
      sgst_rate: 0,
      cgst_rate: 0,
      igst_rate: 0,
      product_id: null,
      available_quantity: 0,
      taxable_value: 0, // Initialize taxable_value to 0
      sgst_amount: 0, // Initialize tax amounts to 0
      cgst_amount: 0,
      igst_amount: 0
    });
  };

  const removeItem = (index) => {
    const removedItem = formData.items[index];
    const newItems = [...formData.items];
    newItems.splice(index, 1);

    // Update serial numbers
    const updatedItems = newItems.map((item, idx) => ({
      ...item,
      sr_no: idx + 1
    }));

    setFormData(prev => ({
      ...prev,
      items: updatedItems
    }));

    // Restore the product quantity
    const updatedProducts = products.map(product => {
      if (product.description === removedItem.description) {
        return {
          ...product,
          available_quantity: product.available_quantity + removedItem.quantity
        };
      }
      return product;
    });

    setProducts(updatedProducts);

    setCurrentItem(prev => ({
      ...prev,
      sr_no: updatedItems.length + 1
    }));
  };

  const calculateTotals = () => {
    // Recalculate all tax amounts for each item to ensure they're correct
    const recalculatedItems = formData.items.map(item => {
      // Ensure all values are properly parsed as numbers
      const quantity = parseFloat(item.quantity) || 0;
      const rate = parseFloat(item.rate) || 0;
      const discount = parseFloat(item.discount) || 0;
      const sgstRate = parseFloat(item.sgst_rate) || 0;
      const cgstRate = parseFloat(item.cgst_rate) || 0;
      const igstRate = parseFloat(item.igst_rate) || 0;

      // Calculate taxable value
      const discountedRate = rate * (1 - discount/100);
      const taxableValue = quantity * discountedRate;

      // Calculate tax amounts
      const sgstAmount = (taxableValue * sgstRate) / 100;
      const cgstAmount = (taxableValue * cgstRate) / 100;
      const igstAmount = (taxableValue * igstRate) / 100;

      return {
        ...item,
        taxable_value: taxableValue,
        sgst_amount: sgstAmount,
        cgst_amount: cgstAmount,
        igst_amount: igstAmount
      };
    });

    // Calculate item-level totals using the recalculated values
    const taxableValue = recalculatedItems.reduce((sum, item) => sum + item.taxable_value, 0);
    const itemSgstAmount = recalculatedItems.reduce((sum, item) => sum + item.sgst_amount, 0);
    const itemCgstAmount = recalculatedItems.reduce((sum, item) => sum + item.cgst_amount, 0);
    const itemIgstAmount = recalculatedItems.reduce((sum, item) => sum + item.igst_amount, 0);

    // Get freight & forwarding amount
    const freightForwarding = parseFloat(formData.freight_forwarding || 0);

    // Calculate total taxable amount (taxable value + freight & forwarding)
    const totalTaxableAmount = taxableValue + freightForwarding;

    // Determine if we should use CGST+SGST or IGST for freight & forwarding
    // based on customer and company state codes
    let ffSgstAmount = 0;
    let ffCgstAmount = 0;
    let ffIgstAmount = 0;

    if (freightForwarding > 0 && formData.customer_id && companyInfo) {
      const selectedCustomer = customers.find(c => c.id === parseInt(formData.customer_id));
      if (selectedCustomer) {
        const isSameState = selectedCustomer.state_code === companyInfo.state_code;

        if (isSameState) {
          // Apply CGST (9%) + SGST (9%) for same state
          ffSgstAmount = (freightForwarding * 9) / 100;
          ffCgstAmount = (freightForwarding * 9) / 100;
        } else {
          // Apply IGST (18%) for different states
          ffIgstAmount = (freightForwarding * 18) / 100;
        }
      }
    }

    // Calculate total tax amounts (items + freight & forwarding)
    const totalSgst = itemSgstAmount + ffSgstAmount;
    const totalCgst = itemCgstAmount + ffCgstAmount;
    const totalIgst = itemIgstAmount + ffIgstAmount;

    // Calculate grand total
    const grandTotal = totalTaxableAmount + totalSgst + totalCgst + totalIgst;

    // Calculate effective tax rates for display
    let totalSgstRate = 0;
    let totalCgstRate = 0;
    let totalIgstRate = 0;

    if (totalTaxableAmount > 0) {
      totalSgstRate = (totalSgst / totalTaxableAmount) * 100;
      totalCgstRate = (totalCgst / totalTaxableAmount) * 100;
      totalIgstRate = (totalIgst / totalTaxableAmount) * 100;
    }

    return {
      taxableValue,
      freightForwarding,
      totalTaxableAmount,
      sgst: totalSgst,
      cgst: totalCgst,
      igst: totalIgst,
      totalSgstRate,
      totalCgstRate,
      totalIgstRate,
      grandTotal,
      // Additional details for debugging
      itemSgstAmount,
      itemCgstAmount,
      itemIgstAmount,
      ffSgstAmount,
      ffCgstAmount,
      ffIgstAmount
    };
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    // Check if at least one item has been added
    if (formData.items.length === 0) {
      showError('Please add at least one item to the invoice');
      return;
    }
    try {
      setLoading(true);

      // Log the form data being sent
      console.log('Submitting invoice data:', formData);

      // Create a copy of the form data to modify
      const dataToSend = {
        ...formData,
        freight_forwarding: parseFloat(formData.freight_forwarding) || 0,
        customer_id: parseInt(formData.customer_id, 10),
        // Include consignee details
        consignee_name: formData.consignee_name,
        consignee_address: formData.consignee_address,
        consignee_state: formData.consignee_state,
        consignee_state_code: formData.consignee_state_code,
        consignee_gstin: formData.consignee_gstin,
        items: formData.items.map(item => ({
          ...item,
          quantity: parseFloat(item.quantity),
          rate: parseFloat(item.rate)
        }))
      };

      // Handle the invoice_date format
      if (dataToSend.invoice_date) {
        const invoiceDateObj = new Date(dataToSend.invoice_date);
        dataToSend.invoice_date = invoiceDateObj.toISOString();
      }

      // Handle the po_date format - if it's empty, don't send it
      if (!dataToSend.po_date) {
        delete dataToSend.po_date;
      } else {
        // Ensure the date is in ISO format (YYYY-MM-DDTHH:mm:ss.sssZ)
        const poDateObj = new Date(dataToSend.po_date);
        dataToSend.po_date = poDateObj.toISOString();
      }

      const response = await axios.post('http://localhost:8000/invoices/', dataToSend);
      console.log('Invoice created successfully:', response.data);

      showSuccess('Invoice created successfully!');
      navigate('/invoices'); // Changed from navigation.navigate to navigate
    } catch (error) {
      console.error('Error creating invoice:', error.response?.data || error.message);

      // Check if it's a subscription limit error
      if (error.response?.status === 403 && error.response?.data?.detail?.includes('Invoice limit reached')) {
        showError(
          <div>
            {error.response.data.detail}
            <br />
            <a href="/subscription" target="_blank" rel="noopener noreferrer" style={{ color: '#007bff', textDecoration: 'underline' }}>
              Upgrade your subscription
            </a>
          </div>
        );
      } else {
        showError('Failed to create invoice');
      }
    } finally {
      setLoading(false);
    }
  };

  const totals = calculateTotals();

  // Add this code right before the return statement in the component
  // This will help us see what values are being used in the condition
  useEffect(() => {
    if (formData.customer_id && companyInfo) {
      const customer = customers.find(c => c.id === parseInt(formData.customer_id));
      console.log('Customer state code:', customer?.state_code);
      console.log('Company state code:', companyInfo?.state_code);
      console.log('Are they equal?', customer?.state_code === companyInfo?.state_code);
    }
  }, [formData.customer_id, companyInfo, customers]);

  // Add debugging for tax calculations
  useEffect(() => {
    if (formData.items.length > 0) {
      console.log('Items with tax details:');
      formData.items.forEach((item, index) => {
        console.log(`Item ${index + 1}: ${item.description}`);
        console.log(`  Taxable Value: ${item.taxable_value}`);
        console.log(`  SGST Rate: ${item.sgst_rate}%, Amount: ${item.sgst_amount}`);
        console.log(`  CGST Rate: ${item.cgst_rate}%, Amount: ${item.cgst_amount}`);
        console.log(`  IGST Rate: ${item.igst_rate}%, Amount: ${item.igst_amount}`);
      });
      console.log('Total tax amounts:');
      console.log(`  Total SGST: ${totals.sgst}`);
      console.log(`  Total CGST: ${totals.cgst}`);
      console.log(`  Total IGST: ${totals.igst}`);
      console.log(`  Grand Total: ${totals.grandTotal}`);
    }
  }, [formData.items, totals]);


  return (
    <div className="invoices-container">
      <div className="invoices-header">
        <div>
          <h2 className="invoices-title">Create New Invoice</h2>
          <p className="invoices-subtitle">Create a new sales invoice for your customers</p>
        </div>
      </div>

      {/* Profile Completion Warning */}
      {profileCompletion && !profileCompletion.profile_completed && (
        <Alert variant="warning" className="mb-4">
          <Alert.Heading>
            <i className="fas fa-exclamation-triangle me-2"></i>
            Complete Your Profile
          </Alert.Heading>
          <p className="mb-3">
            You need to complete your profile before creating invoices. Please fill in the following required sections:
          </p>
          <Row>
            <Col md={6}>
              <h6>Personal Info:</h6>
              <ul className="mb-0">
                <li className={profileCompletion.required_for_invoice?.personal_info?.company_name ? 'text-success' : 'text-danger'}>
                  <i className={`fas ${profileCompletion.required_for_invoice?.personal_info?.company_name ? 'fa-check' : 'fa-times'} me-1`}></i>
                  Company Name
                </li>
                <li className={profileCompletion.required_for_invoice?.personal_info?.mobile_number ? 'text-success' : 'text-danger'}>
                  <i className={`fas ${profileCompletion.required_for_invoice?.personal_info?.mobile_number ? 'fa-check' : 'fa-times'} me-1`}></i>
                  Mobile Number
                </li>
                <li className={profileCompletion.required_for_invoice?.personal_info?.email ? 'text-success' : 'text-danger'}>
                  <i className={`fas ${profileCompletion.required_for_invoice?.personal_info?.email ? 'fa-check' : 'fa-times'} me-1`}></i>
                  Email
                </li>
              </ul>
            </Col>
            <Col md={6}>
              <h6>Company Details:</h6>
              <ul className="mb-0">
                <li className={profileCompletion.required_for_invoice?.company_details?.address ? 'text-success' : 'text-danger'}>
                  <i className={`fas ${profileCompletion.required_for_invoice?.company_details?.address ? 'fa-check' : 'fa-times'} me-1`}></i>
                  Address
                </li>
                <li className={profileCompletion.required_for_invoice?.company_details?.state ? 'text-success' : 'text-danger'}>
                  <i className={`fas ${profileCompletion.required_for_invoice?.company_details?.state ? 'fa-check' : 'fa-times'} me-1`}></i>
                  State
                </li>
                <li className={profileCompletion.required_for_invoice?.company_details?.gstin ? 'text-success' : 'text-danger'}>
                  <i className={`fas ${profileCompletion.required_for_invoice?.company_details?.gstin ? 'fa-check' : 'fa-times'} me-1`}></i>
                  GSTIN
                </li>
              </ul>
            </Col>
          </Row>
          <hr />
          <div className="d-flex justify-content-between align-items-center">
            <span>Profile Completion: <strong>{profileCompletion.profile_completion_percentage}%</strong></span>
            <Button
              variant="primary"
              size="sm"
              onClick={() => window.location.href = '/dashboard?section=profile'}
            >
              <i className="fas fa-user-edit me-1"></i>
              Complete Profile
            </Button>
          </div>
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        <Card className="invoice-card mb-4">
          <Card.Header>
            <h4><i className="fas fa-user-tie"></i> Customer Details</h4>
          </Card.Header>
          <Card.Body>
            <Row className="mb-4">
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label className="invoice-form-label">Invoice Date</Form.Label>
                  <DatePicker
                    selected={formData.invoice_date ? new Date(formData.invoice_date) : new Date()}
                    onChange={(date) => setFormData(prev => ({ ...prev, invoice_date: date ? date.toISOString().split('T')[0] : new Date().toISOString().split('T')[0] }))}
                    className="form-control invoice-form-control"
                    dateFormat="dd/MM/yyyy"
                    portalId="root-portal"
                    popperPlacement="bottom-start"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label className="invoice-form-label">PO Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="po_number"
                    value={formData.po_number}
                    onChange={handleChange}
                    className="invoice-form-control"
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label className="invoice-form-label">PO Date</Form.Label>
                  <DatePicker
                    selected={formData.po_date ? new Date(formData.po_date) : null}
                    onChange={(date) => setFormData(prev => ({ ...prev, po_date: date ? date.toISOString().split('T')[0] : '' }))}
                    className="form-control invoice-form-control"
                    dateFormat="dd/MM/yyyy"
                    portalId="root-portal"
                    popperPlacement="bottom-start"
                    isClearable
                  />
                </Form.Group>
              </Col>
            </Row>

            {/* Buyer (Bill To) Section */}
            <Card className="invoice-card mb-4">
              <Card.Header>
                <h4><i className="fas fa-file-invoice"></i> Buyer (Bill To)</h4>
              </Card.Header>
              <Card.Body>
              <Row>
                <Col md={12}>
                  <Form.Group className="mb-3">
                    <Form.Label className="invoice-form-label">Select Customer</Form.Label>
                    <Form.Select
                      name="customer_id"
                      required
                      value={formData.customer_id}
                      onChange={handleChange}
                      className="invoice-form-select"
                    >
                      <option value="">Select Customer</option>
                      {customers.map(customer => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>

              {formData.customer_id && (
                <div className="selected-customer-details p-3 mb-3 border rounded bg-light">
                  {(() => {
                    const customer = customers.find(c => c.id === parseInt(formData.customer_id));
                    return customer ? (
                      <>
                        <p className="mb-1"><strong>Name:</strong> {customer.name}</p>
                        <p className="mb-1"><strong>Address:</strong> {customer.address}</p>
                        <p className="mb-1"><strong>State:</strong> {customer.state} ({customer.state_code})</p>
                        <p className="mb-1"><strong>GSTIN:</strong> {customer.gstin}</p>
                        <p className="mb-0"><strong>Mobile:</strong> {customer.mobile_number}</p>
                      </>
                    ) : null;
                  })()}
                </div>
              )}
              </Card.Body>
            </Card>

            {/* Consignee (Ship To) Section */}
            <Card className="invoice-card mb-4">
              <Card.Header>
                <h4><i className="fas fa-shipping-fast"></i> Consignee (Ship To)</h4>
              </Card.Header>
              <Card.Body>

              <Form.Check
                type="checkbox"
                id="same-as-buyer"
                label="Same as Buyer"
                checked={sameAsBuyer}
                onChange={handleSameAsBuyer}
                className="mb-3"
              />

              {!sameAsBuyer && (
                <Row>
                  <Col md={12}>
                    <Form.Group className="mb-3">
                      <Form.Label className="invoice-form-label">Select Customer</Form.Label>
                      <Form.Select
                        name="consignee_customer_id"
                        onChange={handleChange}
                        className="invoice-form-select"
                      >
                        <option value="">Select Customer</option>
                        {customers.map(customer => (
                          <option key={customer.id} value={customer.id}>
                            {customer.name}
                          </option>
                        ))}
                      </Form.Select>
                      <Form.Text className="text-muted">
                        Select a customer to auto-fill details or enter manually below
                      </Form.Text>
                    </Form.Group>
                  </Col>
                </Row>
              )}

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label className="invoice-form-label">Name</Form.Label>
                    <Form.Control
                      type="text"
                      name="consignee_name"
                      value={formData.consignee_name}
                      onChange={handleChange}
                      className="invoice-form-control"
                      placeholder="Enter consignee name"
                      required={!sameAsBuyer}
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label className="invoice-form-label">GSTIN</Form.Label>
                    <Form.Control
                      type="text"
                      name="consignee_gstin"
                      value={formData.consignee_gstin}
                      onChange={handleChange}
                      className="invoice-form-control"
                      placeholder="Enter GSTIN"
                      required={!sameAsBuyer}
                    />
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={12}>
                  <Form.Group className="mb-3">
                    <Form.Label className="invoice-form-label">Address</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      name="consignee_address"
                      value={formData.consignee_address}
                      onChange={handleChange}
                      className="invoice-form-control"
                      placeholder="Enter shipping address"
                      required={!sameAsBuyer}
                    />
                    <Form.Text className="text-muted">
                      Always enter the shipping address manually, even when selecting from dropdown
                    </Form.Text>
                  </Form.Group>
                </Col>
              </Row>

              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label className="invoice-form-label">State</Form.Label>
                    <Form.Control
                      type="text"
                      name="consignee_state"
                      value={formData.consignee_state}
                      onChange={handleChange}
                      className="invoice-form-control"
                      placeholder="Enter state"
                      required={!sameAsBuyer}
                    />
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label className="invoice-form-label">State Code</Form.Label>
                    <Form.Control
                      type="text"
                      name="consignee_state_code"
                      value={formData.consignee_state_code}
                      onChange={handleChange}
                      className="invoice-form-control"
                      placeholder="Enter state code"
                      required={!sameAsBuyer}
                    />
                  </Form.Group>
                </Col>
              </Row>
              </Card.Body>
            </Card>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label className="invoice-form-label">Reverse Charge</Form.Label>
                  <Form.Select
                    name="reverse_charge"
                    value={formData.reverse_charge}
                    onChange={handleChange}
                    className="invoice-form-select"
                  >
                    <option value="No">No</option>
                    <option value="Yes">Yes</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label className="invoice-form-label">Freight & Forwarding</Form.Label>
                  <Form.Control
                    type="number"
                    name="freight_forwarding"
                    min="0"
                    step="0.01"
                    value={formData.freight_forwarding}
                    onChange={handleChange}
                    className="invoice-form-control"
                  />
                </Form.Group>
              </Col>
            </Row>

            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label className="invoice-form-label">Vehicle Number</Form.Label>
                  <Form.Control
                    type="text"
                    name="vehicle_number"
                    value={formData.vehicle_number}
                    onChange={handleChange}
                    className="invoice-form-control"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label className="invoice-form-label">Transporter Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="transporter_name"
                    value={formData.transporter_name}
                    onChange={handleChange}
                    className="invoice-form-control"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Card.Body>
        </Card>

        <Card className="invoice-card mb-4">
          <Card.Header>
            <h4><i className="fas fa-shopping-cart"></i> Items</h4>
          </Card.Header>
          <Card.Body>

            <Row>
              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Select Product</Form.Label>
                  <Form.Select
                    onChange={(e) => {
                      const product = products.find(p => p.id === parseInt(e.target.value, 10));
                      if (product) handleProductSelect(product);
                    }}
                  >
                    <option value="">Select Product</option>
                    {products.map(product => (
                      <option
                        key={product.id}
                        value={product.id}
                        disabled={product.available_quantity <= 0}
                      >
                        {product.description} - ₹{product.rate.toFixed(2)} - {product.available_quantity} available
                        {product.available_quantity <= 0 ? ' (Out of Stock)' : ''}
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Col>
            </Row>

            <Row className="align-items-end">
              <Col md={2}>
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control
                    type="text"
                    name="description"
                    value={currentItem.description}
                    onChange={handleItemChange}
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>HSN/SAC</Form.Label>
                  <Form.Control
                    type="text"
                    name="hsn_code"
                    value={currentItem.hsn_code}
                    onChange={handleItemChange}
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>Qty</Form.Label>
                  <Form.Control
                    type="number"
                    name="quantity"
                    min="1"
                    step="1"
                    value={currentItem.quantity}
                    onChange={handleItemChange}
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>UOM</Form.Label>
                  <Form.Control
                    type="text"
                    name="uom"
                    value={currentItem.uom}
                    onChange={handleItemChange}
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>Rate</Form.Label>
                  <Form.Control
                    type="number"
                    name="rate"
                    min="0"
                    step="0.01"
                    value={currentItem.rate}
                    onChange={handleItemChange}
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>Discount (%)</Form.Label>
                  <Form.Control
                    type="number"
                    name="discount"
                    min="0"
                    max="100"
                    step="1"
                    value={currentItem.discount}
                    onChange={handleItemChange}
                  />
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>SGST+CGST</Form.Label>
                  <Form.Select
                    name="sgst_cgst_rate"
                    value={`${currentItem.sgst_rate}+${currentItem.cgst_rate}`}
                    onChange={(e) => {
                      const [sgst, cgst] = e.target.value.split('+').map(parseFloat);
                      setCurrentItem(prev => ({
                        ...prev,
                        sgst_rate: sgst || 0,
                        cgst_rate: cgst || 0,
                        igst_rate: 0 // Reset IGST when SGST+CGST is selected
                      }));
                    }}
                    disabled={
                      !formData.customer_id ||
                      !companyInfo ||
                      (formData.customer_id && customers.find(c => c.id === parseInt(formData.customer_id))?.state_code !== companyInfo?.state_code)
                    }
                  >
                    <option value="0+0">-Select-</option>
                    <option value="0.500+0.500">0.500 + 0.500</option>
                    <option value="0.750+0.750">0.750 + 0.750</option>
                    <option value="3.750+3.750">3.750 + 3.750</option>
                    <option value="0+0">0 + 0</option>
                    <option value="2.500+2.500">2.500 + 2.500</option>
                    <option value="6+6">6 + 6</option>
                    <option value="9+9">9 + 9</option>
                    <option value="14+14">14 + 14</option>
                    <option value="1.500+1.500">1.500 + 1.500</option>
                    <option value="0.050+0.050">0.050 + 0.050</option>
                    <option value="0.125+0.125">0.125 + 0.125</option>
                    <option value="3+3">3 + 3</option>
                    <option value="0+0">Not Appl.</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col>
                <Form.Group className="mb-3">
                  <Form.Label>IGST</Form.Label>
                  <Form.Select
                    name="igst_rate"
                    value={currentItem.igst_rate}
                    onChange={(e) => {
                      const igstRate = parseFloat(e.target.value) || 0;
                      setCurrentItem(prev => ({
                        ...prev,
                        igst_rate: igstRate,
                        sgst_rate: 0, // Reset SGST when IGST is selected
                        cgst_rate: 0  // Reset CGST when IGST is selected
                      }));
                    }}
                    disabled={
                      !formData.customer_id ||
                      !companyInfo ||
                      (formData.customer_id && customers.find(c => c.id === parseInt(formData.customer_id))?.state_code === companyInfo?.state_code)
                    }
                  >
                    <option value="0">-Select-</option>
                    <option value="1">1</option>
                    <option value="1.500">1.500</option>
                    <option value="7.500">7.500</option>
                    <option value="0">0</option>
                    <option value="5">5</option>
                    <option value="12">12</option>
                    <option value="18">18</option>
                    <option value="28">28</option>
                    <option value="3">3</option>
                    <option value="0.100">0.100</option>
                    <option value="0.250">0.250</option>
                    <option value="6">6</option>
                    <option value="0">Not Appl.</option>
                  </Form.Select>
                </Form.Group>
              </Col>
              <Col md={1}>
                <Form.Group className="mb-3">
                  <Form.Label>&nbsp;</Form.Label>
                  <div>
                    <Button
                      className="invoice-btn-icon invoice-btn-primary"
                      onClick={addItem}
                      title="Add Item"
                    >
                      <i className="fas fa-plus"></i>
                    </Button>
                  </div>
                </Form.Group>
              </Col>
            </Row>

            {formData.items.length > 0 && (
              <div className="table-responsive">
              <Table className="items-table mt-4">
                <thead>
                  <tr>
                    <th>Sr. No.</th>
                    <th>Description</th>
                    <th>HSN/SAC Code</th>
                    <th>Qty</th>
                    <th>UOM</th>
                    <th>Rate</th>
                    <th>Discount</th>
                    <th>CGST+SGST</th>
                    <th>IGST</th>
                    <th>Amount</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {formData.items.map((item, index) => (
                    <tr key={index}>
                      <td>{item.sr_no}</td>
                      <td>{item.description}</td>
                      <td>{item.hsn_code}</td>
                      <td>{item.quantity}</td>
                      <td>{item.uom}</td>
                      <td>{item.rate.toFixed(2)}</td>
                      <td>{item.discount > 0 ? `${item.discount}%` : '-'}</td>
                      <td>{item.sgst_rate > 0 ? `${item.sgst_rate}% + ${item.cgst_rate}%` : '-'}</td>
                      <td>{item.igst_rate > 0 ? `${item.igst_rate}%` : '-'}</td>
                      <td>{item.taxable_value.toFixed(2)}</td>
                      <td>
                        <Button
                          className="invoice-btn invoice-btn-danger invoice-btn-sm"
                          onClick={() => removeItem(index)}
                        >
                          <i className="fas fa-trash me-1"></i> Remove
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
              </div>
            )}
          </Card.Body>
        </Card>

        <Card className="invoice-card mb-4">
          <Card.Header>
            <h4><i className="fas fa-calculator"></i> Invoice Totals</h4>
          </Card.Header>
          <Card.Body>

            <div className="invoice-totals">
              <div className="invoice-totals-row">
                <div className="invoice-totals-label">TOTAL TAXABLE VALUE</div>
                <div className="invoice-totals-value">₹{totals.taxableValue.toFixed(2)}</div>
              </div>
              <div className="invoice-totals-row">
                <div className="invoice-totals-label">ADD: FREIGHT & FORWARDING</div>
                <div className="invoice-totals-value">₹{totals.freightForwarding.toFixed(2)}</div>
              </div>
              <div className="invoice-totals-row">
                <div className="invoice-totals-label">TOTAL TAXABLE AMOUNT</div>
                <div className="invoice-totals-value">₹{totals.totalTaxableAmount.toFixed(2)}</div>
              </div>

              {/* SGST Details */}
              {totals.sgst > 0 && (
                <div className="invoice-totals-row">
                  <div className="invoice-totals-label">
                    ADD: SGST
                    {totals.itemSgstAmount > 0 && totals.ffSgstAmount > 0 && (
                      <small className="d-block text-muted">
                        (Items: ₹{totals.itemSgstAmount.toFixed(2)}, F&F: ₹{totals.ffSgstAmount.toFixed(2)})
                      </small>
                    )}
                    {totals.itemSgstAmount > 0 && totals.ffSgstAmount === 0 && (
                      <small className="d-block text-muted">
                        (Items: ₹{totals.itemSgstAmount.toFixed(2)})
                      </small>
                    )}
                    {totals.itemSgstAmount === 0 && totals.ffSgstAmount > 0 && (
                      <small className="d-block text-muted">
                        (F&F: ₹{totals.ffSgstAmount.toFixed(2)})
                      </small>
                    )}
                  </div>
                  <div className="invoice-totals-value">₹{totals.sgst.toFixed(2)}</div>
                </div>
              )}

              {/* CGST Details */}
              {totals.cgst > 0 && (
                <div className="invoice-totals-row">
                  <div className="invoice-totals-label">
                    ADD: CGST
                    {totals.itemCgstAmount > 0 && totals.ffCgstAmount > 0 && (
                      <small className="d-block text-muted">
                        (Items: ₹{totals.itemCgstAmount.toFixed(2)}, F&F: ₹{totals.ffCgstAmount.toFixed(2)})
                      </small>
                    )}
                    {totals.itemCgstAmount > 0 && totals.ffCgstAmount === 0 && (
                      <small className="d-block text-muted">
                        (Items: ₹{totals.itemCgstAmount.toFixed(2)})
                      </small>
                    )}
                    {totals.itemCgstAmount === 0 && totals.ffCgstAmount > 0 && (
                      <small className="d-block text-muted">
                        (F&F: ₹{totals.ffCgstAmount.toFixed(2)})
                      </small>
                    )}
                  </div>
                  <div className="invoice-totals-value">₹{totals.cgst.toFixed(2)}</div>
                </div>
              )}

              {/* IGST Details */}
              {totals.igst > 0 && (
                <div className="invoice-totals-row">
                  <div className="invoice-totals-label">
                    ADD: IGST
                    {totals.itemIgstAmount > 0 && totals.ffIgstAmount > 0 && (
                      <small className="d-block text-muted">
                        (Items: ₹{totals.itemIgstAmount.toFixed(2)}, F&F: ₹{totals.ffIgstAmount.toFixed(2)})
                      </small>
                    )}
                    {totals.itemIgstAmount > 0 && totals.ffIgstAmount === 0 && (
                      <small className="d-block text-muted">
                        (Items: ₹{totals.itemIgstAmount.toFixed(2)})
                      </small>
                    )}
                    {totals.itemIgstAmount === 0 && totals.ffIgstAmount > 0 && (
                      <small className="d-block text-muted">
                        (F&F: ₹{totals.ffIgstAmount.toFixed(2)})
                      </small>
                    )}
                  </div>
                  <div className="invoice-totals-value">₹{totals.igst.toFixed(2)}</div>
                </div>
              )}

              <div className="invoice-totals-row font-weight-bold">
                <div className="invoice-totals-label">INVOICE GRAND TOTAL</div>
                <div className="invoice-totals-value">₹{totals.grandTotal.toFixed(2)}</div>
              </div>
            </div>
          </Card.Body>
        </Card>

        <div className="d-flex justify-content-between" style={{ marginBottom: "0.5in" }}>
          <Button
            className="invoice-btn"
            onClick={() => navigate('/invoices')}
            disabled={loading}
          >
            <i className="fas fa-times me-1"></i> Cancel
          </Button>
          <Button
            className="invoice-btn invoice-btn-primary"
            type="submit"
            disabled={loading || formData.items.length === 0}
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                Creating Invoice...
              </>
            ) : (
              <><i className="fas fa-save me-1"></i> Create Invoice</>
            )}
          </Button>
        </div>
      </Form>

      {/* Success Modal for New Product */}
      <Modal
        show={showSuccessModal}
        onHide={() => setShowSuccessModal(false)}
        centered
        className="product-success-modal"
      >
        <Modal.Header closeButton className="bg-success text-white">
          <Modal.Title><i className="fas fa-check-circle me-2"></i>Product Added Successfully</Modal.Title>
        </Modal.Header>
        <Modal.Body className="py-4">
          <div className="text-center mb-3">
            <div className="success-icon-container mb-3">
              <i className="fas fa-box-open fa-3x text-success"></i>
            </div>
            <h5 className="mb-3">Product "{newProductAdded}" has been successfully added to your product list with available quantity 0.</h5>
            <p className="text-muted">You can manage this product later in the Products section.</p>
          </div>
        </Modal.Body>
        <Modal.Footer className="border-0 justify-content-center">
          <Button
            variant="success"
            size="lg"
            className="px-4"
            onClick={() => setShowSuccessModal(false)}
          >
            Continue
          </Button>
        </Modal.Footer>
      </Modal>
    </div>
  );
}
