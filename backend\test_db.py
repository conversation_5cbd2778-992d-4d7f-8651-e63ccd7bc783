#!/usr/bin/env python3

import sys
import traceback

def test_database_connection():
    try:
        print("Testing database connection...")
        
        # Test basic imports
        print("1. Testing basic imports...")
        from sqlalchemy import create_engine
        print("   ✅ SQLAlchemy imported")
        
        # Test database connection
        print("2. Testing database connection...")
        SQLALCHEMY_DATABASE_URL = "mysql+pymysql://gst_user:admin%40123@localhost/gst_billing"
        engine = create_engine(SQLALCHEMY_DATABASE_URL)
        
        # Test connection
        from sqlalchemy import text
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            print("   ✅ Database connection successful")
        
        # Test models import
        print("3. Testing models import...")
        from models import User
        print("   ✅ Models imported successfully")
        
        print("\n🎉 All tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print(f"Error type: {type(e).__name__}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_database_connection()
    sys.exit(0 if success else 1)
