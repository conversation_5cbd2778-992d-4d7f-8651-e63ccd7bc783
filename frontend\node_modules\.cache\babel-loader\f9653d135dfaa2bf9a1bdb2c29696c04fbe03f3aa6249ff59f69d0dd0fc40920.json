{"ast": null, "code": "var _jsxFileName = \"D:\\\\invoice_project\\\\gst-billing-software\\\\frontend\\\\src\\\\pages\\\\invoices\\\\CreateInvoice.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Form, Button, Card, Row, Col, Table, Modal } from 'react-bootstrap';\nimport axios from 'axios';\nimport { useAuth } from '../../context/AuthContext';\nimport { useToast } from '../../context/ToastContext';\nimport { useNavigate } from 'react-router-dom';\nimport DatePicker from 'react-datepicker';\nimport 'react-datepicker/dist/react-datepicker.css';\nimport '../../styles/invoices.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function CreateInvoice() {\n  _s();\n  var _profileCompletion$re, _profileCompletion$re2, _profileCompletion$re3, _profileCompletion$re4, _profileCompletion$re5, _profileCompletion$re6, _profileCompletion$re7, _profileCompletion$re8, _profileCompletion$re9, _profileCompletion$re0, _profileCompletion$re1, _profileCompletion$re10, _profileCompletion$re11, _profileCompletion$re12, _profileCompletion$re13, _profileCompletion$re14, _profileCompletion$re15, _profileCompletion$re16, _profileCompletion$re17, _profileCompletion$re18, _profileCompletion$re19, _profileCompletion$re20, _profileCompletion$re21, _profileCompletion$re22, _customers$find, _customers$find2;\n  const [customers, setCustomers] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [companyInfo, setCompanyInfo] = useState(null);\n  const [profileCompletion, setProfileCompletion] = useState(null);\n  // const [invoiceNumber, setInvoiceNumber] = useState(''); // Removed unused state\n\n  const [formData, setFormData] = useState({\n    customer_id: '',\n    invoice_date: new Date().toISOString().split('T')[0],\n    // Set default to today\n    po_number: '',\n    po_date: '',\n    reverse_charge: 'No',\n    freight_forwarding: 0,\n    vehicle_number: '',\n    transporter_name: '',\n    // Consignee (Ship To) details\n    consignee_name: '',\n    consignee_address: '',\n    consignee_state: '',\n    consignee_state_code: '',\n    consignee_gstin: '',\n    items: []\n  });\n\n  // State to track if consignee is same as buyer\n  const [sameAsBuyer, setSameAsBuyer] = useState(false);\n  const [currentItem, setCurrentItem] = useState({\n    sr_no: 1,\n    description: '',\n    hsn_code: '',\n    quantity: 1,\n    uom: 'NOS',\n    rate: 0,\n    discount: 0,\n    // Explicitly set to 0 (number)\n    sgst_rate: 0,\n    cgst_rate: 0,\n    igst_rate: 0,\n    product_id: null,\n    available_quantity: 0,\n    taxable_value: 0,\n    // Initialize taxable_value to 0\n    sgst_amount: 0,\n    // Tax amount for SGST\n    cgst_amount: 0,\n    // Tax amount for CGST\n    igst_amount: 0 // Tax amount for IGST\n  });\n  const [loading, setLoading] = useState(false);\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\n  const [newProductAdded, setNewProductAdded] = useState('');\n  const {\n    currentUser\n  } = useAuth();\n  const {\n    showError,\n    showSuccess,\n    showWarning\n  } = useToast();\n  const navigate = useNavigate();\n  useEffect(() => {\n    if (currentUser) {\n      fetchCustomers();\n      fetchProducts();\n      fetchCompanyInfo();\n      generateInvoiceNumber();\n      fetchProfileCompletion();\n    }\n  }, [currentUser]);\n  const fetchProfileCompletion = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/profile/completion');\n      setProfileCompletion(response.data);\n    } catch (error) {\n      console.error('Error fetching profile completion:', error);\n    }\n  };\n\n  // Effect to handle GST rates based on state codes\n  useEffect(() => {\n    if (formData.customer_id && companyInfo) {\n      const selectedCustomer = customers.find(c => c.id === parseInt(formData.customer_id));\n      if (selectedCustomer) {\n        // If state codes match, enable SGST+CGST and disable IGST\n        // If state codes don't match, enable IGST and disable SGST+CGST\n        const isSameState = selectedCustomer.state_code === companyInfo.state_code;\n\n        // Update current item to reset tax rates based on state\n        setCurrentItem(prev => ({\n          ...prev,\n          sgst_rate: isSameState ? prev.sgst_rate : 0,\n          cgst_rate: isSameState ? prev.cgst_rate : 0,\n          igst_rate: !isSameState ? prev.igst_rate : 0\n        }));\n      }\n    }\n  }, [formData.customer_id, companyInfo, customers]);\n  const fetchCompanyInfo = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/company'); // Remove trailing slash\n      setCompanyInfo(response.data);\n    } catch (error) {\n      console.error('Error fetching company info:', error);\n    }\n  };\n  const generateInvoiceNumber = async () => {\n    try {\n      // Get the latest invoice number\n      const response = await axios.get('http://localhost:8000/invoices/latest-number/');\n\n      // Generate company prefix\n      let prefix = '';\n      if (companyInfo && companyInfo.name) {\n        // Extract first letter of each word\n        prefix = companyInfo.name.split(' ').map(word => word.charAt(0).toUpperCase()).join('');\n      }\n\n      // Determine financial year\n      const today = new Date();\n      let financialYear;\n      if (today.getMonth() >= 3) {\n        // April onwards\n        financialYear = `${today.getFullYear()}-${today.getFullYear() + 1 - 2000}`;\n      } else {\n        financialYear = `${today.getFullYear() - 1}-${today.getFullYear() - 2000}`;\n      }\n\n      // Get the serial number and increment it\n      let serialNumber = 1;\n      if (response.data && response.data.latest_number) {\n        serialNumber = parseInt(response.data.latest_number) + 1;\n      }\n\n      // Format the invoice number\n      const formattedSerialNumber = serialNumber.toString().padStart(2, '0');\n      const invoiceNumber = `${prefix}/${financialYear}/${formattedSerialNumber}`;\n\n      // Add the invoice number to the form data\n      setFormData(prev => ({\n        ...prev,\n        invoice_number: invoiceNumber\n      }));\n    } catch (error) {\n      console.error('Error generating invoice number:', error);\n    }\n  };\n  const fetchCustomers = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/customers/');\n      setCustomers(response.data);\n    } catch (error) {\n      console.error('Error fetching customers:', error);\n    }\n  };\n  const fetchProducts = async () => {\n    try {\n      const response = await axios.get('http://localhost:8000/products/');\n      setProducts(response.data);\n    } catch (error) {\n      console.error('Error fetching products:', error);\n    }\n  };\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n\n    // Handle customer selection for buyer\n    if (name === 'customer_id') {\n      const customerId = parseInt(value, 10) || '';\n      const selectedCustomer = customers.find(c => c.id === customerId);\n      setFormData(prev => ({\n        ...prev,\n        customer_id: customerId,\n        // If \"Same as Buyer\" is checked, update consignee details too\n        ...(sameAsBuyer && selectedCustomer ? {\n          consignee_name: selectedCustomer.name,\n          consignee_address: selectedCustomer.address,\n          consignee_state: selectedCustomer.state,\n          consignee_state_code: selectedCustomer.state_code,\n          consignee_gstin: selectedCustomer.gstin\n        } : {})\n      }));\n      return;\n    }\n\n    // Handle consignee customer selection\n    if (name === 'consignee_customer_id') {\n      const consigneeId = parseInt(value, 10) || '';\n      const selectedConsignee = customers.find(c => c.id === consigneeId);\n      if (selectedConsignee) {\n        setFormData(prev => ({\n          ...prev,\n          consignee_name: selectedConsignee.name,\n          consignee_state: selectedConsignee.state,\n          consignee_state_code: selectedConsignee.state_code,\n          consignee_gstin: selectedConsignee.gstin\n          // Note: We don't auto-fill address as it needs to be manually entered\n        }));\n      }\n      return;\n    }\n\n    // Handle all other fields\n    setFormData(prev => ({\n      ...prev,\n      [name]: name === 'freight_forwarding' ? parseFloat(value) || 0 : value\n    }));\n  };\n\n  // Handle \"Same as Buyer\" checkbox\n  const handleSameAsBuyer = e => {\n    const isChecked = e.target.checked;\n    setSameAsBuyer(isChecked);\n    if (isChecked) {\n      // Get the selected customer\n      const selectedCustomer = customers.find(c => c.id === formData.customer_id);\n      if (selectedCustomer) {\n        setFormData(prev => ({\n          ...prev,\n          consignee_name: selectedCustomer.name,\n          consignee_address: selectedCustomer.address,\n          consignee_state: selectedCustomer.state,\n          consignee_state_code: selectedCustomer.state_code,\n          consignee_gstin: selectedCustomer.gstin\n        }));\n      }\n    } else {\n      // Clear consignee fields if unchecked\n      setFormData(prev => ({\n        ...prev,\n        consignee_name: '',\n        consignee_address: '',\n        consignee_state: '',\n        consignee_state_code: '',\n        consignee_gstin: ''\n      }));\n    }\n  };\n  const handleItemChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    if (name === 'quantity') {\n      const newQuantity = parseFloat(value) || 0;\n\n      // Check if the quantity exceeds available quantity\n      if (newQuantity > currentItem.available_quantity) {\n        showError(`Only ${currentItem.available_quantity} units available for ${currentItem.description}`);\n        return;\n      }\n    }\n    setCurrentItem(prev => {\n      // Handle numeric fields with proper parsing and default to 0 if invalid\n      let newValue;\n      if (name === 'quantity' || name === 'rate' || name === 'sgst_rate' || name === 'cgst_rate' || name === 'igst_rate') {\n        newValue = parseFloat(value) || 0;\n      } else if (name === 'discount') {\n        newValue = value === '' ? 0 : parseFloat(value) || 0;\n      } else {\n        newValue = value;\n      }\n      const newItem = {\n        ...prev,\n        [name]: newValue\n      };\n\n      // Calculate taxable value if quantity, rate, or discount changes\n      if (name === 'quantity' || name === 'rate' || name === 'discount') {\n        // Ensure discount is treated as a number\n        const discount = parseFloat(newItem.discount) || 0;\n        const discountedRate = newItem.rate * (1 - discount / 100);\n        newItem.taxable_value = newItem.quantity * discountedRate;\n      }\n\n      // Calculate tax amounts if taxable value or tax rates change\n      if (name === 'quantity' || name === 'rate' || name === 'discount' || name === 'sgst_rate' || name === 'cgst_rate' || name === 'igst_rate') {\n        // Calculate tax amounts based on taxable value\n        newItem.sgst_amount = newItem.taxable_value * newItem.sgst_rate / 100;\n        newItem.cgst_amount = newItem.taxable_value * newItem.cgst_rate / 100;\n        newItem.igst_amount = newItem.taxable_value * newItem.igst_rate / 100;\n      }\n      return newItem;\n    });\n  };\n  const handleProductSelect = product => {\n    // Check if product is already in the invoice items\n    const existingItemIndex = formData.items.findIndex(item => item.description === product.description);\n\n    // Calculate available quantity after accounting for items already in the invoice\n    let adjustedAvailableQuantity = product.available_quantity;\n    if (existingItemIndex !== -1) {\n      adjustedAvailableQuantity += formData.items[existingItemIndex].quantity;\n    }\n\n    // Don't allow selecting products with no available quantity\n    if (adjustedAvailableQuantity <= 0) {\n      showError(`Product ${product.description} is out of stock`);\n      return;\n    }\n\n    // No need to check state codes here as GST rates are initialized to 0\n\n    setCurrentItem({\n      sr_no: formData.items.length + 1,\n      description: product.description,\n      hsn_code: product.hsn_code,\n      quantity: 1,\n      uom: product.uom,\n      rate: product.rate,\n      discount: 0,\n      // Explicitly set to 0 (number)\n      product_id: product.id,\n      available_quantity: adjustedAvailableQuantity,\n      // Store the available quantity for validation\n      // Initialize GST rates to 0 - user must select them manually\n      sgst_rate: 0,\n      cgst_rate: 0,\n      igst_rate: 0,\n      taxable_value: product.rate,\n      // Initialize taxable_value to rate (since quantity is 1 and discount is 0)\n      sgst_amount: 0,\n      // Initialize tax amounts to 0\n      cgst_amount: 0,\n      igst_amount: 0\n    });\n  };\n\n  // Search functionality removed as requested\n\n  const addItem = async () => {\n    if (!currentItem.description || !currentItem.hsn_code || currentItem.rate <= 0) {\n      showError('Please fill all item fields and ensure rate is positive');\n      return;\n    }\n    if (currentItem.quantity <= 0) {\n      showError('Quantity must be greater than zero');\n      return;\n    }\n\n    // Check if the product already exists in the database\n    let productExists = false;\n    let productId = currentItem.product_id;\n\n    // If no product_id, check if a product with the same description exists\n    if (!productId) {\n      const existingProduct = products.find(p => p.description.toLowerCase() === currentItem.description.toLowerCase());\n      if (existingProduct) {\n        productExists = true;\n        productId = existingProduct.id;\n        // Update current item with the existing product details\n        setCurrentItem(prev => ({\n          ...prev,\n          product_id: existingProduct.id,\n          hsn_code: existingProduct.hsn_code,\n          uom: existingProduct.uom,\n          rate: existingProduct.rate,\n          available_quantity: existingProduct.available_quantity\n        }));\n      } else {\n        // Create a new product with available_quantity = 0\n        try {\n          const newProduct = {\n            description: currentItem.description,\n            hsn_code: currentItem.hsn_code,\n            available_quantity: 0,\n            // Set to 0 as requested\n            uom: currentItem.uom,\n            rate: currentItem.rate\n          };\n          const response = await axios.post('http://localhost:8000/products/', newProduct);\n          productId = response.data.id;\n\n          // Add the new product to the products array\n          setProducts(prev => [...prev, response.data]);\n\n          // Set the product_id in the current item\n          setCurrentItem(prev => ({\n            ...prev,\n            product_id: productId\n          }));\n\n          // Show success message\n          setNewProductAdded(currentItem.description);\n          setShowSuccessModal(true);\n        } catch (error) {\n          console.error('Error creating product:', error);\n          showError('Failed to create new product. Please try again.');\n          return;\n        }\n      }\n    }\n\n    // Check if the product is already in the invoice\n    const existingItemIndex = formData.items.findIndex(item => item.description === currentItem.description);\n    if (existingItemIndex !== -1) {\n      // Update existing item instead of adding a new one\n      const updatedItems = [...formData.items];\n      const existingItem = updatedItems[existingItemIndex];\n\n      // Only check available quantity if it's an existing product with inventory\n      if (productExists && currentItem.available_quantity > 0) {\n        // Check if the combined quantity exceeds available quantity\n        const totalQuantity = existingItem.quantity + currentItem.quantity;\n        if (totalQuantity > currentItem.available_quantity) {\n          showError(`Cannot add ${currentItem.quantity} more units. Only ${currentItem.available_quantity - existingItem.quantity} units available.`);\n          return;\n        }\n      }\n\n      // Update the existing item\n      // Ensure all values are properly parsed as numbers\n      const rate = parseFloat(existingItem.rate) || 0;\n      const discount = parseFloat(existingItem.discount) || 0;\n      const existingQuantity = parseFloat(existingItem.quantity) || 0;\n      const additionalQuantity = parseFloat(currentItem.quantity) || 0;\n      const newQuantity = existingQuantity + additionalQuantity;\n\n      // Calculate taxable value with discount\n      const discountedRate = rate * (1 - discount / 100);\n      const newTaxableValue = newQuantity * discountedRate;\n\n      // Ensure tax rates are properly parsed as numbers\n      const sgstRate = parseFloat(existingItem.sgst_rate) || 0;\n      const cgstRate = parseFloat(existingItem.cgst_rate) || 0;\n      const igstRate = parseFloat(existingItem.igst_rate) || 0;\n\n      // Calculate tax amounts\n      const sgstAmount = newTaxableValue * sgstRate / 100;\n      const cgstAmount = newTaxableValue * cgstRate / 100;\n      const igstAmount = newTaxableValue * igstRate / 100;\n      updatedItems[existingItemIndex] = {\n        ...existingItem,\n        rate: rate,\n        discount: discount,\n        quantity: newQuantity,\n        taxable_value: newTaxableValue,\n        sgst_rate: sgstRate,\n        cgst_rate: cgstRate,\n        igst_rate: igstRate,\n        sgst_amount: sgstAmount,\n        cgst_amount: cgstAmount,\n        igst_amount: igstAmount\n      };\n      setFormData(prev => ({\n        ...prev,\n        items: updatedItems\n      }));\n    } else {\n      // Add as a new item\n      // Ensure all values are properly parsed as numbers\n      const quantity = parseFloat(currentItem.quantity) || 0;\n      const rate = parseFloat(currentItem.rate) || 0;\n      const discount = parseFloat(currentItem.discount) || 0;\n\n      // Calculate taxable value with discount\n      const discountedRate = rate * (1 - discount / 100);\n      const taxableValue = quantity * discountedRate;\n\n      // Ensure tax rates are properly parsed as numbers\n      const sgstRate = parseFloat(currentItem.sgst_rate) || 0;\n      const cgstRate = parseFloat(currentItem.cgst_rate) || 0;\n      const igstRate = parseFloat(currentItem.igst_rate) || 0;\n\n      // Calculate tax amounts\n      const sgstAmount = taxableValue * sgstRate / 100;\n      const cgstAmount = taxableValue * cgstRate / 100;\n      const igstAmount = taxableValue * igstRate / 100;\n      const newItem = {\n        ...currentItem,\n        quantity: quantity,\n        rate: rate,\n        discount: discount,\n        taxable_value: taxableValue,\n        product_id: productId,\n        sgst_rate: sgstRate,\n        cgst_rate: cgstRate,\n        igst_rate: igstRate,\n        sgst_amount: sgstAmount,\n        cgst_amount: cgstAmount,\n        igst_amount: igstAmount\n      };\n      setFormData(prev => ({\n        ...prev,\n        items: [...prev.items, newItem]\n      }));\n    }\n\n    // Update the products array to reflect the reduced quantity (only for existing products)\n    if (productExists) {\n      const updatedProducts = products.map(product => {\n        if (product.id === productId) {\n          return {\n            ...product,\n            available_quantity: product.available_quantity - currentItem.quantity\n          };\n        }\n        return product;\n      });\n      setProducts(updatedProducts);\n    }\n\n    // Reset current item\n    setCurrentItem({\n      sr_no: formData.items.length + 2,\n      description: '',\n      hsn_code: '',\n      quantity: 1,\n      uom: 'NOS',\n      rate: 0,\n      discount: 0,\n      // Explicitly set to 0 (number)\n      sgst_rate: 0,\n      cgst_rate: 0,\n      igst_rate: 0,\n      product_id: null,\n      available_quantity: 0,\n      taxable_value: 0,\n      // Initialize taxable_value to 0\n      sgst_amount: 0,\n      // Initialize tax amounts to 0\n      cgst_amount: 0,\n      igst_amount: 0\n    });\n  };\n  const removeItem = index => {\n    const removedItem = formData.items[index];\n    const newItems = [...formData.items];\n    newItems.splice(index, 1);\n\n    // Update serial numbers\n    const updatedItems = newItems.map((item, idx) => ({\n      ...item,\n      sr_no: idx + 1\n    }));\n    setFormData(prev => ({\n      ...prev,\n      items: updatedItems\n    }));\n\n    // Restore the product quantity\n    const updatedProducts = products.map(product => {\n      if (product.description === removedItem.description) {\n        return {\n          ...product,\n          available_quantity: product.available_quantity + removedItem.quantity\n        };\n      }\n      return product;\n    });\n    setProducts(updatedProducts);\n    setCurrentItem(prev => ({\n      ...prev,\n      sr_no: updatedItems.length + 1\n    }));\n  };\n  const calculateTotals = () => {\n    // Recalculate all tax amounts for each item to ensure they're correct\n    const recalculatedItems = formData.items.map(item => {\n      // Ensure all values are properly parsed as numbers\n      const quantity = parseFloat(item.quantity) || 0;\n      const rate = parseFloat(item.rate) || 0;\n      const discount = parseFloat(item.discount) || 0;\n      const sgstRate = parseFloat(item.sgst_rate) || 0;\n      const cgstRate = parseFloat(item.cgst_rate) || 0;\n      const igstRate = parseFloat(item.igst_rate) || 0;\n\n      // Calculate taxable value\n      const discountedRate = rate * (1 - discount / 100);\n      const taxableValue = quantity * discountedRate;\n\n      // Calculate tax amounts\n      const sgstAmount = taxableValue * sgstRate / 100;\n      const cgstAmount = taxableValue * cgstRate / 100;\n      const igstAmount = taxableValue * igstRate / 100;\n      return {\n        ...item,\n        taxable_value: taxableValue,\n        sgst_amount: sgstAmount,\n        cgst_amount: cgstAmount,\n        igst_amount: igstAmount\n      };\n    });\n\n    // Calculate item-level totals using the recalculated values\n    const taxableValue = recalculatedItems.reduce((sum, item) => sum + item.taxable_value, 0);\n    const itemSgstAmount = recalculatedItems.reduce((sum, item) => sum + item.sgst_amount, 0);\n    const itemCgstAmount = recalculatedItems.reduce((sum, item) => sum + item.cgst_amount, 0);\n    const itemIgstAmount = recalculatedItems.reduce((sum, item) => sum + item.igst_amount, 0);\n\n    // Get freight & forwarding amount\n    const freightForwarding = parseFloat(formData.freight_forwarding || 0);\n\n    // Calculate total taxable amount (taxable value + freight & forwarding)\n    const totalTaxableAmount = taxableValue + freightForwarding;\n\n    // Determine if we should use CGST+SGST or IGST for freight & forwarding\n    // based on customer and company state codes\n    let ffSgstAmount = 0;\n    let ffCgstAmount = 0;\n    let ffIgstAmount = 0;\n    if (freightForwarding > 0 && formData.customer_id && companyInfo) {\n      const selectedCustomer = customers.find(c => c.id === parseInt(formData.customer_id));\n      if (selectedCustomer) {\n        const isSameState = selectedCustomer.state_code === companyInfo.state_code;\n        if (isSameState) {\n          // Apply CGST (9%) + SGST (9%) for same state\n          ffSgstAmount = freightForwarding * 9 / 100;\n          ffCgstAmount = freightForwarding * 9 / 100;\n        } else {\n          // Apply IGST (18%) for different states\n          ffIgstAmount = freightForwarding * 18 / 100;\n        }\n      }\n    }\n\n    // Calculate total tax amounts (items + freight & forwarding)\n    const totalSgst = itemSgstAmount + ffSgstAmount;\n    const totalCgst = itemCgstAmount + ffCgstAmount;\n    const totalIgst = itemIgstAmount + ffIgstAmount;\n\n    // Calculate grand total\n    const grandTotal = totalTaxableAmount + totalSgst + totalCgst + totalIgst;\n\n    // Calculate effective tax rates for display\n    let totalSgstRate = 0;\n    let totalCgstRate = 0;\n    let totalIgstRate = 0;\n    if (totalTaxableAmount > 0) {\n      totalSgstRate = totalSgst / totalTaxableAmount * 100;\n      totalCgstRate = totalCgst / totalTaxableAmount * 100;\n      totalIgstRate = totalIgst / totalTaxableAmount * 100;\n    }\n    return {\n      taxableValue,\n      freightForwarding,\n      totalTaxableAmount,\n      sgst: totalSgst,\n      cgst: totalCgst,\n      igst: totalIgst,\n      totalSgstRate,\n      totalCgstRate,\n      totalIgstRate,\n      grandTotal,\n      // Additional details for debugging\n      itemSgstAmount,\n      itemCgstAmount,\n      itemIgstAmount,\n      ffSgstAmount,\n      ffCgstAmount,\n      ffIgstAmount\n    };\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    // Check if at least one item has been added\n    if (formData.items.length === 0) {\n      showError('Please add at least one item to the invoice');\n      return;\n    }\n    try {\n      setLoading(true);\n\n      // Log the form data being sent\n      console.log('Submitting invoice data:', formData);\n\n      // Create a copy of the form data to modify\n      const dataToSend = {\n        ...formData,\n        freight_forwarding: parseFloat(formData.freight_forwarding) || 0,\n        customer_id: parseInt(formData.customer_id, 10),\n        // Include consignee details\n        consignee_name: formData.consignee_name,\n        consignee_address: formData.consignee_address,\n        consignee_state: formData.consignee_state,\n        consignee_state_code: formData.consignee_state_code,\n        consignee_gstin: formData.consignee_gstin,\n        items: formData.items.map(item => ({\n          ...item,\n          quantity: parseFloat(item.quantity),\n          rate: parseFloat(item.rate)\n        }))\n      };\n\n      // Handle the invoice_date format\n      if (dataToSend.invoice_date) {\n        const invoiceDateObj = new Date(dataToSend.invoice_date);\n        dataToSend.invoice_date = invoiceDateObj.toISOString();\n      }\n\n      // Handle the po_date format - if it's empty, don't send it\n      if (!dataToSend.po_date) {\n        delete dataToSend.po_date;\n      } else {\n        // Ensure the date is in ISO format (YYYY-MM-DDTHH:mm:ss.sssZ)\n        const poDateObj = new Date(dataToSend.po_date);\n        dataToSend.po_date = poDateObj.toISOString();\n      }\n      const response = await axios.post('http://localhost:8000/invoices/', dataToSend);\n      console.log('Invoice created successfully:', response.data);\n      showSuccess('Invoice created successfully!');\n      navigate('/invoices'); // Changed from navigation.navigate to navigate\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response3$data, _error$response3$data2;\n      console.error('Error creating invoice:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n\n      // Check if it's a subscription limit error\n      if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 403 && (_error$response3 = error.response) !== null && _error$response3 !== void 0 && (_error$response3$data = _error$response3.data) !== null && _error$response3$data !== void 0 && (_error$response3$data2 = _error$response3$data.detail) !== null && _error$response3$data2 !== void 0 && _error$response3$data2.includes('Invoice limit reached')) {\n        showError(/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [error.response.data.detail, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 752,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/subscription\",\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            style: {\n              color: '#007bff',\n              textDecoration: 'underline'\n            },\n            children: \"Upgrade your subscription\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 753,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 11\n        }, this));\n      } else {\n        showError('Failed to create invoice');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  const totals = calculateTotals();\n\n  // Add this code right before the return statement in the component\n  // This will help us see what values are being used in the condition\n  useEffect(() => {\n    if (formData.customer_id && companyInfo) {\n      const customer = customers.find(c => c.id === parseInt(formData.customer_id));\n      console.log('Customer state code:', customer === null || customer === void 0 ? void 0 : customer.state_code);\n      console.log('Company state code:', companyInfo === null || companyInfo === void 0 ? void 0 : companyInfo.state_code);\n      console.log('Are they equal?', (customer === null || customer === void 0 ? void 0 : customer.state_code) === (companyInfo === null || companyInfo === void 0 ? void 0 : companyInfo.state_code));\n    }\n  }, [formData.customer_id, companyInfo, customers]);\n\n  // Add debugging for tax calculations\n  useEffect(() => {\n    if (formData.items.length > 0) {\n      console.log('Items with tax details:');\n      formData.items.forEach((item, index) => {\n        console.log(`Item ${index + 1}: ${item.description}`);\n        console.log(`  Taxable Value: ${item.taxable_value}`);\n        console.log(`  SGST Rate: ${item.sgst_rate}%, Amount: ${item.sgst_amount}`);\n        console.log(`  CGST Rate: ${item.cgst_rate}%, Amount: ${item.cgst_amount}`);\n        console.log(`  IGST Rate: ${item.igst_rate}%, Amount: ${item.igst_amount}`);\n      });\n      console.log('Total tax amounts:');\n      console.log(`  Total SGST: ${totals.sgst}`);\n      console.log(`  Total CGST: ${totals.cgst}`);\n      console.log(`  Total IGST: ${totals.igst}`);\n      console.log(`  Grand Total: ${totals.grandTotal}`);\n    }\n  }, [formData.items, totals]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"invoices-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"invoices-header\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"invoices-title\",\n          children: \"Create New Invoice\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 803,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"invoices-subtitle\",\n          children: \"Create a new sales invoice for your customers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 804,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 802,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 801,\n      columnNumber: 7\n    }, this), profileCompletion && !profileCompletion.profile_completed && /*#__PURE__*/_jsxDEV(Alert, {\n      variant: \"warning\",\n      className: \"mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(Alert.Heading, {\n        children: [/*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-exclamation-triangle me-2\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 812,\n          columnNumber: 13\n        }, this), \"Complete Your Profile\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 811,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"mb-3\",\n        children: \"You need to complete your profile before creating invoices. Please fill in the following required sections:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 815,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Row, {\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Personal Info:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 820,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: (_profileCompletion$re = profileCompletion.required_for_invoice) !== null && _profileCompletion$re !== void 0 && (_profileCompletion$re2 = _profileCompletion$re.personal_info) !== null && _profileCompletion$re2 !== void 0 && _profileCompletion$re2.company_name ? 'text-success' : 'text-danger',\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${(_profileCompletion$re3 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re3 !== void 0 && (_profileCompletion$re4 = _profileCompletion$re3.personal_info) !== null && _profileCompletion$re4 !== void 0 && _profileCompletion$re4.company_name ? 'fa-check' : 'fa-times'} me-1`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 19\n              }, this), \"Company Name\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 822,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: (_profileCompletion$re5 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re5 !== void 0 && (_profileCompletion$re6 = _profileCompletion$re5.personal_info) !== null && _profileCompletion$re6 !== void 0 && _profileCompletion$re6.mobile_number ? 'text-success' : 'text-danger',\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${(_profileCompletion$re7 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re7 !== void 0 && (_profileCompletion$re8 = _profileCompletion$re7.personal_info) !== null && _profileCompletion$re8 !== void 0 && _profileCompletion$re8.mobile_number ? 'fa-check' : 'fa-times'} me-1`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 827,\n                columnNumber: 19\n              }, this), \"Mobile Number\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: (_profileCompletion$re9 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re9 !== void 0 && (_profileCompletion$re0 = _profileCompletion$re9.personal_info) !== null && _profileCompletion$re0 !== void 0 && _profileCompletion$re0.email ? 'text-success' : 'text-danger',\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${(_profileCompletion$re1 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re1 !== void 0 && (_profileCompletion$re10 = _profileCompletion$re1.personal_info) !== null && _profileCompletion$re10 !== void 0 && _profileCompletion$re10.email ? 'fa-check' : 'fa-times'} me-1`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 19\n              }, this), \"Email\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 821,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 819,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          md: 6,\n          children: [/*#__PURE__*/_jsxDEV(\"h6\", {\n            children: \"Company Details:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 837,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"mb-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              className: (_profileCompletion$re11 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re11 !== void 0 && (_profileCompletion$re12 = _profileCompletion$re11.company_details) !== null && _profileCompletion$re12 !== void 0 && _profileCompletion$re12.address ? 'text-success' : 'text-danger',\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${(_profileCompletion$re13 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re13 !== void 0 && (_profileCompletion$re14 = _profileCompletion$re13.company_details) !== null && _profileCompletion$re14 !== void 0 && _profileCompletion$re14.address ? 'fa-check' : 'fa-times'} me-1`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 840,\n                columnNumber: 19\n              }, this), \"Address\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 839,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: (_profileCompletion$re15 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re15 !== void 0 && (_profileCompletion$re16 = _profileCompletion$re15.company_details) !== null && _profileCompletion$re16 !== void 0 && _profileCompletion$re16.state ? 'text-success' : 'text-danger',\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${(_profileCompletion$re17 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re17 !== void 0 && (_profileCompletion$re18 = _profileCompletion$re17.company_details) !== null && _profileCompletion$re18 !== void 0 && _profileCompletion$re18.state ? 'fa-check' : 'fa-times'} me-1`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 844,\n                columnNumber: 19\n              }, this), \"State\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 843,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              className: (_profileCompletion$re19 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re19 !== void 0 && (_profileCompletion$re20 = _profileCompletion$re19.company_details) !== null && _profileCompletion$re20 !== void 0 && _profileCompletion$re20.gstin ? 'text-success' : 'text-danger',\n              children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                className: `fas ${(_profileCompletion$re21 = profileCompletion.required_for_invoice) !== null && _profileCompletion$re21 !== void 0 && (_profileCompletion$re22 = _profileCompletion$re21.company_details) !== null && _profileCompletion$re22 !== void 0 && _profileCompletion$re22.gstin ? 'fa-check' : 'fa-times'} me-1`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 19\n              }, this), \"GSTIN\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 838,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 836,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 818,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"hr\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between align-items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"Profile Completion: \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: [profileCompletion.profile_completion_percentage, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 856,\n            columnNumber: 39\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"primary\",\n          size: \"sm\",\n          onClick: () => window.location.href = '/dashboard?section=profile',\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user-edit me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 862,\n            columnNumber: 15\n          }, this), \"Complete Profile\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 855,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 810,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Form, {\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(Card, {\n        className: \"invoice-card mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-user-tie\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 872,\n              columnNumber: 17\n            }, this), \" Customer Details\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            className: \"mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"invoice-form-label\",\n                  children: \"Invoice Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                  selected: formData.invoice_date ? new Date(formData.invoice_date) : new Date(),\n                  onChange: date => setFormData(prev => ({\n                    ...prev,\n                    invoice_date: date ? date.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]\n                  })),\n                  className: \"form-control invoice-form-control\",\n                  dateFormat: \"dd/MM/yyyy\",\n                  portalId: \"root-portal\",\n                  popperPlacement: \"bottom-start\",\n                  required: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 876,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"invoice-form-label\",\n                  children: \"PO Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 892,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"po_number\",\n                  value: formData.po_number,\n                  onChange: handleChange,\n                  className: \"invoice-form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 891,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 4,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"invoice-form-label\",\n                  children: \"PO Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 904,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(DatePicker, {\n                  selected: formData.po_date ? new Date(formData.po_date) : null,\n                  onChange: date => setFormData(prev => ({\n                    ...prev,\n                    po_date: date ? date.toISOString().split('T')[0] : ''\n                  })),\n                  className: \"form-control invoice-form-control\",\n                  dateFormat: \"dd/MM/yyyy\",\n                  portalId: \"root-portal\",\n                  popperPlacement: \"bottom-start\",\n                  isClearable: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 905,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 903,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 875,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"invoice-card mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-file-invoice\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 921,\n                  columnNumber: 21\n                }, this), \" Buyer (Bill To)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 921,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 920,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(Row, {\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"invoice-form-label\",\n                      children: \"Select Customer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 927,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      name: \"customer_id\",\n                      required: true,\n                      value: formData.customer_id,\n                      onChange: handleChange,\n                      className: \"invoice-form-select\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Customer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 23\n                      }, this), customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: customer.id,\n                        children: customer.name\n                      }, customer.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 937,\n                        columnNumber: 25\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 928,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 926,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 925,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 924,\n                columnNumber: 15\n              }, this), formData.customer_id && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"selected-customer-details p-3 mb-3 border rounded bg-light\",\n                children: (() => {\n                  const customer = customers.find(c => c.id === parseInt(formData.customer_id));\n                  return customer ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Name:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 952,\n                        columnNumber: 45\n                      }, this), \" \", customer.name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 952,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Address:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 953,\n                        columnNumber: 45\n                      }, this), \" \", customer.address]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 953,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"State:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 954,\n                        columnNumber: 45\n                      }, this), \" \", customer.state, \" (\", customer.state_code, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 954,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"GSTIN:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 955,\n                        columnNumber: 45\n                      }, this), \" \", customer.gstin]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 955,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"mb-0\",\n                      children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                        children: \"Mobile:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 956,\n                        columnNumber: 45\n                      }, this), \" \", customer.mobile_number]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 956,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true) : null;\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 947,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 923,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 919,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Card, {\n            className: \"invoice-card mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              children: /*#__PURE__*/_jsxDEV(\"h4\", {\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-shipping-fast\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 21\n                }, this), \" Consignee (Ship To)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 968,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 967,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              children: [/*#__PURE__*/_jsxDEV(Form.Check, {\n                type: \"checkbox\",\n                id: \"same-as-buyer\",\n                label: \"Same as Buyer\",\n                checked: sameAsBuyer,\n                onChange: handleSameAsBuyer,\n                className: \"mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 15\n              }, this), !sameAsBuyer && /*#__PURE__*/_jsxDEV(Row, {\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"invoice-form-label\",\n                      children: \"Select Customer\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 985,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                      name: \"consignee_customer_id\",\n                      onChange: handleChange,\n                      className: \"invoice-form-select\",\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"\",\n                        children: \"Select Customer\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 991,\n                        columnNumber: 25\n                      }, this), customers.map(customer => /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: customer.id,\n                        children: customer.name\n                      }, customer.id, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 993,\n                        columnNumber: 27\n                      }, this))]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 986,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                      className: \"text-muted\",\n                      children: \"Select a customer to auto-fill details or enter manually below\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 998,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 982,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"invoice-form-label\",\n                      children: \"Name\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1009,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      name: \"consignee_name\",\n                      value: formData.consignee_name,\n                      onChange: handleChange,\n                      className: \"invoice-form-control\",\n                      placeholder: \"Enter consignee name\",\n                      required: !sameAsBuyer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1008,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1007,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"invoice-form-label\",\n                      children: \"GSTIN\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1023,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      name: \"consignee_gstin\",\n                      value: formData.consignee_gstin,\n                      onChange: handleChange,\n                      className: \"invoice-form-control\",\n                      placeholder: \"Enter GSTIN\",\n                      required: !sameAsBuyer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1022,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1021,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1006,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  md: 12,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"invoice-form-label\",\n                      children: \"Address\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1040,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      as: \"textarea\",\n                      rows: 3,\n                      name: \"consignee_address\",\n                      value: formData.consignee_address,\n                      onChange: handleChange,\n                      className: \"invoice-form-control\",\n                      placeholder: \"Enter shipping address\",\n                      required: !sameAsBuyer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1041,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Text, {\n                      className: \"text-muted\",\n                      children: \"Always enter the shipping address manually, even when selecting from dropdown\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1051,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1039,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1038,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"invoice-form-label\",\n                      children: \"State\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      name: \"consignee_state\",\n                      value: formData.consignee_state,\n                      onChange: handleChange,\n                      className: \"invoice-form-control\",\n                      placeholder: \"Enter state\",\n                      required: !sameAsBuyer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1060,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  md: 6,\n                  children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"invoice-form-label\",\n                      children: \"State Code\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1075,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"text\",\n                      name: \"consignee_state_code\",\n                      value: formData.consignee_state_code,\n                      onChange: handleChange,\n                      className: \"invoice-form-control\",\n                      placeholder: \"Enter state code\",\n                      required: !sameAsBuyer\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1074,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1073,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1058,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 970,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 966,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"invoice-form-label\",\n                  children: \"Reverse Charge\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1094,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"reverse_charge\",\n                  value: formData.reverse_charge,\n                  onChange: handleChange,\n                  className: \"invoice-form-select\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"No\",\n                    children: \"No\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1101,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"Yes\",\n                    children: \"Yes\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1102,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1095,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1093,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1092,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"invoice-form-label\",\n                  children: \"Freight & Forwarding\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1108,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"freight_forwarding\",\n                  min: \"0\",\n                  step: \"0.01\",\n                  value: formData.freight_forwarding,\n                  onChange: handleChange,\n                  className: \"invoice-form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1109,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1107,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1106,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1091,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"invoice-form-label\",\n                  children: \"Vehicle Number\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1125,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"vehicle_number\",\n                  value: formData.vehicle_number,\n                  onChange: handleChange,\n                  className: \"invoice-form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1126,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1124,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  className: \"invoice-form-label\",\n                  children: \"Transporter Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"transporter_name\",\n                  value: formData.transporter_name,\n                  onChange: handleChange,\n                  className: \"invoice-form-control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1136,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1135,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1122,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 874,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"invoice-card mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-shopping-cart\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1153,\n              columnNumber: 17\n            }, this), \" Items\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: [/*#__PURE__*/_jsxDEV(Row, {\n            children: /*#__PURE__*/_jsxDEV(Col, {\n              md: 12,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Select Product\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1160,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  onChange: e => {\n                    const product = products.find(p => p.id === parseInt(e.target.value, 10));\n                    if (product) handleProductSelect(product);\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"Select Product\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1167,\n                    columnNumber: 21\n                  }, this), products.map(product => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: product.id,\n                    disabled: product.available_quantity <= 0,\n                    children: [product.description, \" - \\u20B9\", product.rate.toFixed(2), \" - \", product.available_quantity, \" available\", product.available_quantity <= 0 ? ' (Out of Stock)' : '']\n                  }, product.id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1169,\n                    columnNumber: 23\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1161,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1159,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1158,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1157,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Row, {\n            className: \"align-items-end\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              md: 2,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Description\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"description\",\n                  value: currentItem.description,\n                  onChange: handleItemChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1187,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"HSN/SAC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"hsn_code\",\n                  value: currentItem.hsn_code,\n                  onChange: handleItemChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1198,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1196,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1195,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Qty\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"quantity\",\n                  min: \"1\",\n                  step: \"1\",\n                  value: currentItem.quantity,\n                  onChange: handleItemChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1209,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"UOM\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"uom\",\n                  value: currentItem.uom,\n                  onChange: handleItemChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1222,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1220,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Rate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1232,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"rate\",\n                  min: \"0\",\n                  step: \"0.01\",\n                  value: currentItem.rate,\n                  onChange: handleItemChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1233,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1231,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1230,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Discount (%)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"number\",\n                  name: \"discount\",\n                  min: \"0\",\n                  max: \"100\",\n                  step: \"1\",\n                  value: currentItem.discount,\n                  onChange: handleItemChange\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1246,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1244,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"SGST+CGST\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1259,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"sgst_cgst_rate\",\n                  value: `${currentItem.sgst_rate}+${currentItem.cgst_rate}`,\n                  onChange: e => {\n                    const [sgst, cgst] = e.target.value.split('+').map(parseFloat);\n                    setCurrentItem(prev => ({\n                      ...prev,\n                      sgst_rate: sgst || 0,\n                      cgst_rate: cgst || 0,\n                      igst_rate: 0 // Reset IGST when SGST+CGST is selected\n                    }));\n                  },\n                  disabled: !formData.customer_id || !companyInfo || formData.customer_id && ((_customers$find = customers.find(c => c.id === parseInt(formData.customer_id))) === null || _customers$find === void 0 ? void 0 : _customers$find.state_code) !== (companyInfo === null || companyInfo === void 0 ? void 0 : companyInfo.state_code),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0+0\",\n                    children: \"-Select-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1278,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0.500+0.500\",\n                    children: \"0.500 + 0.500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1279,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0.750+0.750\",\n                    children: \"0.750 + 0.750\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1280,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"3.750+3.750\",\n                    children: \"3.750 + 3.750\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1281,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0+0\",\n                    children: \"0 + 0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1282,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"2.500+2.500\",\n                    children: \"2.500 + 2.500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1283,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"6+6\",\n                    children: \"6 + 6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1284,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"9+9\",\n                    children: \"9 + 9\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1285,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"14+14\",\n                    children: \"14 + 14\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1286,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"1.500+1.500\",\n                    children: \"1.500 + 1.500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1287,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0.050+0.050\",\n                    children: \"0.050 + 0.050\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1288,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0.125+0.125\",\n                    children: \"0.125 + 0.125\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1289,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"3+3\",\n                    children: \"3 + 3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1290,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0+0\",\n                    children: \"Not Appl.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1291,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1260,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1258,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1257,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"IGST\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1297,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Select, {\n                  name: \"igst_rate\",\n                  value: currentItem.igst_rate,\n                  onChange: e => {\n                    const igstRate = parseFloat(e.target.value) || 0;\n                    setCurrentItem(prev => ({\n                      ...prev,\n                      igst_rate: igstRate,\n                      sgst_rate: 0,\n                      // Reset SGST when IGST is selected\n                      cgst_rate: 0 // Reset CGST when IGST is selected\n                    }));\n                  },\n                  disabled: !formData.customer_id || !companyInfo || formData.customer_id && ((_customers$find2 = customers.find(c => c.id === parseInt(formData.customer_id))) === null || _customers$find2 === void 0 ? void 0 : _customers$find2.state_code) === (companyInfo === null || companyInfo === void 0 ? void 0 : companyInfo.state_code),\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0\",\n                    children: \"-Select-\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1316,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"1\",\n                    children: \"1\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1317,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"1.500\",\n                    children: \"1.500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1318,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"7.500\",\n                    children: \"7.500\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1319,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0\",\n                    children: \"0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1320,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"5\",\n                    children: \"5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1321,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"12\",\n                    children: \"12\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1322,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"18\",\n                    children: \"18\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1323,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"28\",\n                    children: \"28\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1324,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"3\",\n                    children: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1325,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0.100\",\n                    children: \"0.100\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1326,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0.250\",\n                    children: \"0.250\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1327,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"6\",\n                    children: \"6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1328,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"0\",\n                    children: \"Not Appl.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1329,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1298,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1296,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1295,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              md: 1,\n              children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"\\xA0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1335,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    className: \"invoice-btn-icon invoice-btn-primary\",\n                    onClick: addItem,\n                    title: \"Add Item\",\n                    children: /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-plus\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1342,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1337,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1334,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1333,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1183,\n            columnNumber: 13\n          }, this), formData.items.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"table-responsive\",\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              className: \"items-table mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Sr. No.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1354,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1355,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"HSN/SAC Code\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1356,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Qty\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1357,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"UOM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1358,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Rate\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1359,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Discount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1360,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"CGST+SGST\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1361,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"IGST\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1362,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Amount\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1363,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    children: \"Action\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1364,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1353,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1352,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: formData.items.map((item, index) => /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.sr_no\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1370,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1371,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.hsn_code\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1372,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.quantity\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1373,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.uom\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1374,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.rate.toFixed(2)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1375,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.discount > 0 ? `${item.discount}%` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1376,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.sgst_rate > 0 ? `${item.sgst_rate}% + ${item.cgst_rate}%` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1377,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.igst_rate > 0 ? `${item.igst_rate}%` : '-'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1378,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: item.taxable_value.toFixed(2)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1379,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      className: \"invoice-btn invoice-btn-danger invoice-btn-sm\",\n                      onClick: () => removeItem(index),\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-trash me-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1385,\n                        columnNumber: 27\n                      }, this), \" Remove\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1381,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1380,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1369,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1367,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1351,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1350,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        className: \"invoice-card mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n          children: /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-calculator\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1399,\n              columnNumber: 17\n            }, this), \" Invoice Totals\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1399,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1398,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"invoice-totals\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-totals-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-label\",\n                children: \"TOTAL TAXABLE VALUE\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-value\",\n                children: [\"\\u20B9\", totals.taxableValue.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1406,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-totals-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-label\",\n                children: \"ADD: FREIGHT & FORWARDING\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1409,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-value\",\n                children: [\"\\u20B9\", totals.freightForwarding.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1410,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1408,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-totals-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-label\",\n                children: \"TOTAL TAXABLE AMOUNT\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1413,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-value\",\n                children: [\"\\u20B9\", totals.totalTaxableAmount.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1414,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1412,\n              columnNumber: 15\n            }, this), totals.sgst > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-totals-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-label\",\n                children: [\"ADD: SGST\", totals.itemSgstAmount > 0 && totals.ffSgstAmount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(Items: \\u20B9\", totals.itemSgstAmount.toFixed(2), \", F&F: \\u20B9\", totals.ffSgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1423,\n                  columnNumber: 23\n                }, this), totals.itemSgstAmount > 0 && totals.ffSgstAmount === 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(Items: \\u20B9\", totals.itemSgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1428,\n                  columnNumber: 23\n                }, this), totals.itemSgstAmount === 0 && totals.ffSgstAmount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(F&F: \\u20B9\", totals.ffSgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1433,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1420,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-value\",\n                children: [\"\\u20B9\", totals.sgst.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1438,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1419,\n              columnNumber: 17\n            }, this), totals.cgst > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-totals-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-label\",\n                children: [\"ADD: CGST\", totals.itemCgstAmount > 0 && totals.ffCgstAmount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(Items: \\u20B9\", totals.itemCgstAmount.toFixed(2), \", F&F: \\u20B9\", totals.ffCgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1448,\n                  columnNumber: 23\n                }, this), totals.itemCgstAmount > 0 && totals.ffCgstAmount === 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(Items: \\u20B9\", totals.itemCgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1453,\n                  columnNumber: 23\n                }, this), totals.itemCgstAmount === 0 && totals.ffCgstAmount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(F&F: \\u20B9\", totals.ffCgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1458,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1445,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-value\",\n                children: [\"\\u20B9\", totals.cgst.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1444,\n              columnNumber: 17\n            }, this), totals.igst > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-totals-row\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-label\",\n                children: [\"ADD: IGST\", totals.itemIgstAmount > 0 && totals.ffIgstAmount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(Items: \\u20B9\", totals.itemIgstAmount.toFixed(2), \", F&F: \\u20B9\", totals.ffIgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1473,\n                  columnNumber: 23\n                }, this), totals.itemIgstAmount > 0 && totals.ffIgstAmount === 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(Items: \\u20B9\", totals.itemIgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1478,\n                  columnNumber: 23\n                }, this), totals.itemIgstAmount === 0 && totals.ffIgstAmount > 0 && /*#__PURE__*/_jsxDEV(\"small\", {\n                  className: \"d-block text-muted\",\n                  children: [\"(F&F: \\u20B9\", totals.ffIgstAmount.toFixed(2), \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1483,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1470,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-value\",\n                children: [\"\\u20B9\", totals.igst.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1488,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1469,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"invoice-totals-row font-weight-bold\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-label\",\n                children: \"INVOICE GRAND TOTAL\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1493,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"invoice-totals-value\",\n                children: [\"\\u20B9\", totals.grandTotal.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1494,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1492,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1403,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1401,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"d-flex justify-content-between\",\n        style: {\n          marginBottom: \"0.5in\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          className: \"invoice-btn\",\n          onClick: () => navigate('/invoices'),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-times me-1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1506,\n            columnNumber: 13\n          }, this), \" Cancel\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1501,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          className: \"invoice-btn invoice-btn-primary\",\n          type: \"submit\",\n          disabled: loading || formData.items.length === 0,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"spinner-border spinner-border-sm me-2\",\n              role: \"status\",\n              \"aria-hidden\": \"true\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1515,\n              columnNumber: 17\n            }, this), \"Creating Invoice...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-save me-1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1519,\n              columnNumber: 17\n            }, this), \" Create Invoice\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1508,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1500,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 869,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      show: showSuccessModal,\n      onHide: () => setShowSuccessModal(false),\n      centered: true,\n      className: \"product-success-modal\",\n      children: [/*#__PURE__*/_jsxDEV(Modal.Header, {\n        closeButton: true,\n        className: \"bg-success text-white\",\n        children: /*#__PURE__*/_jsxDEV(Modal.Title, {\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-check-circle me-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1533,\n            columnNumber: 24\n          }, this), \"Product Added Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1533,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1532,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Body, {\n        className: \"py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"success-icon-container mb-3\",\n            children: /*#__PURE__*/_jsxDEV(\"i\", {\n              className: \"fas fa-box-open fa-3x text-success\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1538,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1537,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n            className: \"mb-3\",\n            children: [\"Product \\\"\", newProductAdded, \"\\\" has been successfully added to your product list with available quantity 0.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1540,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-muted\",\n            children: \"You can manage this product later in the Products section.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1541,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1536,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1535,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Modal.Footer, {\n        className: \"border-0 justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"success\",\n          size: \"lg\",\n          className: \"px-4\",\n          onClick: () => setShowSuccessModal(false),\n          children: \"Continue\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1545,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1544,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1526,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 800,\n    columnNumber: 5\n  }, this);\n}\n_s(CreateInvoice, \"TL65f+8UXyJDL8mptFociKlmEwo=\", false, function () {\n  return [useAuth, useToast, useNavigate];\n});\n_c = CreateInvoice;\nvar _c;\n$RefreshReg$(_c, \"CreateInvoice\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Form", "<PERSON><PERSON>", "Card", "Row", "Col", "Table", "Modal", "axios", "useAuth", "useToast", "useNavigate", "DatePicker", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "CreateInvoice", "_s", "_profileCompletion$re", "_profileCompletion$re2", "_profileCompletion$re3", "_profileCompletion$re4", "_profileCompletion$re5", "_profileCompletion$re6", "_profileCompletion$re7", "_profileCompletion$re8", "_profileCompletion$re9", "_profileCompletion$re0", "_profileCompletion$re1", "_profileCompletion$re10", "_profileCompletion$re11", "_profileCompletion$re12", "_profileCompletion$re13", "_profileCompletion$re14", "_profileCompletion$re15", "_profileCompletion$re16", "_profileCompletion$re17", "_profileCompletion$re18", "_profileCompletion$re19", "_profileCompletion$re20", "_profileCompletion$re21", "_profileCompletion$re22", "_customers$find", "_customers$find2", "customers", "setCustomers", "products", "setProducts", "companyInfo", "setCompanyInfo", "profileCompletion", "setProfileCompletion", "formData", "setFormData", "customer_id", "invoice_date", "Date", "toISOString", "split", "po_number", "po_date", "reverse_charge", "freight_forwarding", "vehicle_number", "transporter_name", "consignee_name", "consignee_address", "consignee_state", "consignee_state_code", "consignee_gstin", "items", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "currentItem", "setCurrentItem", "sr_no", "description", "hsn_code", "quantity", "uom", "rate", "discount", "sgst_rate", "cgst_rate", "igst_rate", "product_id", "available_quantity", "taxable_value", "sgst_amount", "cgst_amount", "igst_amount", "loading", "setLoading", "showSuccessModal", "setShowSuccessModal", "newProductAdded", "setNewProductAdded", "currentUser", "showError", "showSuccess", "showWarning", "navigate", "fetchCustomers", "fetchProducts", "fetchCompanyInfo", "generateInvoiceNumber", "fetchProfileCompletion", "response", "get", "data", "error", "console", "selectedCustomer", "find", "c", "id", "parseInt", "isSameState", "state_code", "prev", "prefix", "name", "map", "word", "char<PERSON>t", "toUpperCase", "join", "today", "financialYear", "getMonth", "getFullYear", "serialNumber", "latest_number", "formattedSerialNumber", "toString", "padStart", "invoiceNumber", "invoice_number", "handleChange", "e", "value", "target", "customerId", "address", "state", "gstin", "consigneeId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFloat", "handleSameAsBuyer", "isChecked", "checked", "handleItemChange", "newQuantity", "newValue", "newItem", "discountedRate", "handleProductSelect", "product", "existingItemIndex", "findIndex", "item", "adjustedAvailableQuantity", "length", "addItem", "productExists", "productId", "existingProduct", "p", "toLowerCase", "newProduct", "post", "updatedItems", "existingItem", "totalQuantity", "existingQuantity", "additionalQuantity", "newTaxableValue", "sgstRate", "cgstRate", "igstRate", "sgstAmount", "cgstAmount", "igstAmount", "taxableValue", "updatedProducts", "removeItem", "index", "removedItem", "newItems", "splice", "idx", "calculateTotals", "recalculatedItems", "reduce", "sum", "itemSgstAmount", "itemCgstAmount", "itemIgstAmount", "freightForwarding", "totalTaxableAmount", "ffSgstAmount", "ffCgstAmount", "ffIgstAmount", "totalSgst", "totalCgst", "totalIgst", "grandTotal", "totalSgstRate", "totalCgstRate", "totalIgstRate", "sgst", "cgst", "igst", "handleSubmit", "preventDefault", "log", "dataToSend", "invoiceDateObj", "poDateObj", "_error$response", "_error$response2", "_error$response3", "_error$response3$data", "_error$response3$data2", "message", "status", "detail", "includes", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "rel", "style", "color", "textDecoration", "totals", "customer", "for<PERSON>ach", "className", "profile_completed", "<PERSON><PERSON>", "variant", "Heading", "md", "required_for_invoice", "personal_info", "company_name", "mobile_number", "email", "company_details", "profile_completion_percentage", "size", "onClick", "window", "location", "onSubmit", "Header", "Body", "Group", "Label", "selected", "onChange", "date", "dateFormat", "portalId", "popperPlacement", "required", "Control", "type", "isClearable", "Select", "Check", "label", "Text", "placeholder", "as", "rows", "min", "step", "disabled", "toFixed", "max", "title", "marginBottom", "role", "show", "onHide", "centered", "closeButton", "Title", "Footer", "_c", "$RefreshReg$"], "sources": ["D:/invoice_project/gst-billing-software/frontend/src/pages/invoices/CreateInvoice.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { Form, Button, Card, Row, Col, Table, Modal } from 'react-bootstrap';\r\nimport axios from 'axios';\r\nimport { useAuth } from '../../context/AuthContext';\r\nimport { useToast } from '../../context/ToastContext';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport DatePicker from 'react-datepicker';\r\nimport 'react-datepicker/dist/react-datepicker.css';\r\nimport '../../styles/invoices.css';\r\n\r\nexport default function CreateInvoice() {\r\n  const [customers, setCustomers] = useState([]);\r\n  const [products, setProducts] = useState([]);\r\n  const [companyInfo, setCompanyInfo] = useState(null);\r\n  const [profileCompletion, setProfileCompletion] = useState(null);\r\n  // const [invoiceNumber, setInvoiceNumber] = useState(''); // Removed unused state\r\n\r\n  const [formData, setFormData] = useState({\r\n    customer_id: '',\r\n    invoice_date: new Date().toISOString().split('T')[0], // Set default to today\r\n    po_number: '',\r\n    po_date: '',\r\n    reverse_charge: 'No',\r\n    freight_forwarding: 0,\r\n    vehicle_number: '',\r\n    transporter_name: '',\r\n    // Consignee (Ship To) details\r\n    consignee_name: '',\r\n    consignee_address: '',\r\n    consignee_state: '',\r\n    consignee_state_code: '',\r\n    consignee_gstin: '',\r\n    items: []\r\n  });\r\n\r\n  // State to track if consignee is same as buyer\r\n  const [sameAsBuyer, setSameAsBuyer] = useState(false);\r\n  const [currentItem, setCurrentItem] = useState({\r\n    sr_no: 1,\r\n    description: '',\r\n    hsn_code: '',\r\n    quantity: 1,\r\n    uom: 'NOS',\r\n    rate: 0,\r\n    discount: 0, // Explicitly set to 0 (number)\r\n    sgst_rate: 0,\r\n    cgst_rate: 0,\r\n    igst_rate: 0,\r\n    product_id: null,\r\n    available_quantity: 0,\r\n    taxable_value: 0, // Initialize taxable_value to 0\r\n    sgst_amount: 0, // Tax amount for SGST\r\n    cgst_amount: 0, // Tax amount for CGST\r\n    igst_amount: 0  // Tax amount for IGST\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const [showSuccessModal, setShowSuccessModal] = useState(false);\r\n  const [newProductAdded, setNewProductAdded] = useState('');\r\n  const { currentUser } = useAuth();\r\n  const { showError, showSuccess, showWarning } = useToast();\r\n  const navigate = useNavigate();\r\n\r\n  useEffect(() => {\r\n    if (currentUser) {\r\n      fetchCustomers();\r\n      fetchProducts();\r\n      fetchCompanyInfo();\r\n      generateInvoiceNumber();\r\n      fetchProfileCompletion();\r\n    }\r\n  }, [currentUser]);\r\n\r\n  const fetchProfileCompletion = async () => {\r\n    try {\r\n      const response = await axios.get('http://localhost:8000/profile/completion');\r\n      setProfileCompletion(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching profile completion:', error);\r\n    }\r\n  };\r\n\r\n  // Effect to handle GST rates based on state codes\r\n  useEffect(() => {\r\n    if (formData.customer_id && companyInfo) {\r\n      const selectedCustomer = customers.find(c => c.id === parseInt(formData.customer_id));\r\n      if (selectedCustomer) {\r\n        // If state codes match, enable SGST+CGST and disable IGST\r\n        // If state codes don't match, enable IGST and disable SGST+CGST\r\n        const isSameState = selectedCustomer.state_code === companyInfo.state_code;\r\n\r\n        // Update current item to reset tax rates based on state\r\n        setCurrentItem(prev => ({\r\n          ...prev,\r\n          sgst_rate: isSameState ? prev.sgst_rate : 0,\r\n          cgst_rate: isSameState ? prev.cgst_rate : 0,\r\n          igst_rate: !isSameState ? prev.igst_rate : 0\r\n        }));\r\n      }\r\n    }\r\n  }, [formData.customer_id, companyInfo, customers]);\r\n\r\n  const fetchCompanyInfo = async () => {\r\n    try {\r\n      const response = await axios.get('http://localhost:8000/company'); // Remove trailing slash\r\n      setCompanyInfo(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching company info:', error);\r\n    }\r\n  };\r\n\r\n  const generateInvoiceNumber = async () => {\r\n    try {\r\n      // Get the latest invoice number\r\n      const response = await axios.get('http://localhost:8000/invoices/latest-number/');\r\n\r\n      // Generate company prefix\r\n      let prefix = '';\r\n      if (companyInfo && companyInfo.name) {\r\n        // Extract first letter of each word\r\n        prefix = companyInfo.name\r\n          .split(' ')\r\n          .map(word => word.charAt(0).toUpperCase())\r\n          .join('');\r\n      }\r\n\r\n      // Determine financial year\r\n      const today = new Date();\r\n      let financialYear;\r\n      if (today.getMonth() >= 3) { // April onwards\r\n        financialYear = `${today.getFullYear()}-${today.getFullYear() + 1 - 2000}`;\r\n      } else {\r\n        financialYear = `${today.getFullYear() - 1}-${today.getFullYear() - 2000}`;\r\n      }\r\n\r\n      // Get the serial number and increment it\r\n      let serialNumber = 1;\r\n      if (response.data && response.data.latest_number) {\r\n        serialNumber = parseInt(response.data.latest_number) + 1;\r\n      }\r\n\r\n      // Format the invoice number\r\n      const formattedSerialNumber = serialNumber.toString().padStart(2, '0');\r\n      const invoiceNumber = `${prefix}/${financialYear}/${formattedSerialNumber}`;\r\n\r\n      // Add the invoice number to the form data\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        invoice_number: invoiceNumber\r\n      }));\r\n\r\n    } catch (error) {\r\n      console.error('Error generating invoice number:', error);\r\n    }\r\n  };\r\n\r\n\r\n  const fetchCustomers = async () => {\r\n    try {\r\n      const response = await axios.get('http://localhost:8000/customers/');\r\n      setCustomers(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching customers:', error);\r\n    }\r\n  };\r\n\r\n  const fetchProducts = async () => {\r\n    try {\r\n      const response = await axios.get('http://localhost:8000/products/');\r\n      setProducts(response.data);\r\n    } catch (error) {\r\n      console.error('Error fetching products:', error);\r\n    }\r\n  };\r\n\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Handle customer selection for buyer\r\n    if (name === 'customer_id') {\r\n      const customerId = parseInt(value, 10) || '';\r\n      const selectedCustomer = customers.find(c => c.id === customerId);\r\n\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        customer_id: customerId,\r\n        // If \"Same as Buyer\" is checked, update consignee details too\r\n        ...(sameAsBuyer && selectedCustomer ? {\r\n          consignee_name: selectedCustomer.name,\r\n          consignee_address: selectedCustomer.address,\r\n          consignee_state: selectedCustomer.state,\r\n          consignee_state_code: selectedCustomer.state_code,\r\n          consignee_gstin: selectedCustomer.gstin\r\n        } : {})\r\n      }));\r\n      return;\r\n    }\r\n\r\n    // Handle consignee customer selection\r\n    if (name === 'consignee_customer_id') {\r\n      const consigneeId = parseInt(value, 10) || '';\r\n      const selectedConsignee = customers.find(c => c.id === consigneeId);\r\n\r\n      if (selectedConsignee) {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          consignee_name: selectedConsignee.name,\r\n          consignee_state: selectedConsignee.state,\r\n          consignee_state_code: selectedConsignee.state_code,\r\n          consignee_gstin: selectedConsignee.gstin,\r\n          // Note: We don't auto-fill address as it needs to be manually entered\r\n        }));\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Handle all other fields\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: name === 'freight_forwarding' ? parseFloat(value) || 0 : value\r\n    }));\r\n  };\r\n\r\n  // Handle \"Same as Buyer\" checkbox\r\n  const handleSameAsBuyer = (e) => {\r\n    const isChecked = e.target.checked;\r\n    setSameAsBuyer(isChecked);\r\n\r\n    if (isChecked) {\r\n      // Get the selected customer\r\n      const selectedCustomer = customers.find(c => c.id === formData.customer_id);\r\n\r\n      if (selectedCustomer) {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          consignee_name: selectedCustomer.name,\r\n          consignee_address: selectedCustomer.address,\r\n          consignee_state: selectedCustomer.state,\r\n          consignee_state_code: selectedCustomer.state_code,\r\n          consignee_gstin: selectedCustomer.gstin\r\n        }));\r\n      }\r\n    } else {\r\n      // Clear consignee fields if unchecked\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        consignee_name: '',\r\n        consignee_address: '',\r\n        consignee_state: '',\r\n        consignee_state_code: '',\r\n        consignee_gstin: ''\r\n      }));\r\n    }\r\n  };\r\n\r\n\r\n  const handleItemChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    if (name === 'quantity') {\r\n      const newQuantity = parseFloat(value) || 0;\r\n\r\n      // Check if the quantity exceeds available quantity\r\n      if (newQuantity > currentItem.available_quantity) {\r\n        showError(`Only ${currentItem.available_quantity} units available for ${currentItem.description}`);\r\n        return;\r\n      }\r\n    }\r\n\r\n    setCurrentItem(prev => {\r\n      // Handle numeric fields with proper parsing and default to 0 if invalid\r\n      let newValue;\r\n      if (name === 'quantity' || name === 'rate' || name === 'sgst_rate' || name === 'cgst_rate' || name === 'igst_rate') {\r\n        newValue = parseFloat(value) || 0;\r\n      } else if (name === 'discount') {\r\n        newValue = value === '' ? 0 : parseFloat(value) || 0;\r\n      } else {\r\n        newValue = value;\r\n      }\r\n\r\n      const newItem = {\r\n        ...prev,\r\n        [name]: newValue\r\n      };\r\n\r\n      // Calculate taxable value if quantity, rate, or discount changes\r\n      if (name === 'quantity' || name === 'rate' || name === 'discount') {\r\n        // Ensure discount is treated as a number\r\n        const discount = parseFloat(newItem.discount) || 0;\r\n        const discountedRate = newItem.rate * (1 - discount/100);\r\n        newItem.taxable_value = newItem.quantity * discountedRate;\r\n      }\r\n\r\n      // Calculate tax amounts if taxable value or tax rates change\r\n      if (name === 'quantity' || name === 'rate' || name === 'discount' ||\r\n          name === 'sgst_rate' || name === 'cgst_rate' || name === 'igst_rate') {\r\n        // Calculate tax amounts based on taxable value\r\n        newItem.sgst_amount = (newItem.taxable_value * newItem.sgst_rate) / 100;\r\n        newItem.cgst_amount = (newItem.taxable_value * newItem.cgst_rate) / 100;\r\n        newItem.igst_amount = (newItem.taxable_value * newItem.igst_rate) / 100;\r\n      }\r\n\r\n      return newItem;\r\n    });\r\n  };\r\n\r\n  const handleProductSelect = (product) => {\r\n    // Check if product is already in the invoice items\r\n    const existingItemIndex = formData.items.findIndex(item => item.description === product.description);\r\n\r\n    // Calculate available quantity after accounting for items already in the invoice\r\n    let adjustedAvailableQuantity = product.available_quantity;\r\n    if (existingItemIndex !== -1) {\r\n      adjustedAvailableQuantity += formData.items[existingItemIndex].quantity;\r\n    }\r\n\r\n    // Don't allow selecting products with no available quantity\r\n    if (adjustedAvailableQuantity <= 0) {\r\n      showError(`Product ${product.description} is out of stock`);\r\n      return;\r\n    }\r\n\r\n    // No need to check state codes here as GST rates are initialized to 0\r\n\r\n    setCurrentItem({\r\n      sr_no: formData.items.length + 1,\r\n      description: product.description,\r\n      hsn_code: product.hsn_code,\r\n      quantity: 1,\r\n      uom: product.uom,\r\n      rate: product.rate,\r\n      discount: 0, // Explicitly set to 0 (number)\r\n      product_id: product.id,\r\n      available_quantity: adjustedAvailableQuantity, // Store the available quantity for validation\r\n      // Initialize GST rates to 0 - user must select them manually\r\n      sgst_rate: 0,\r\n      cgst_rate: 0,\r\n      igst_rate: 0,\r\n      taxable_value: product.rate, // Initialize taxable_value to rate (since quantity is 1 and discount is 0)\r\n      sgst_amount: 0, // Initialize tax amounts to 0\r\n      cgst_amount: 0,\r\n      igst_amount: 0\r\n    });\r\n\r\n\r\n  };\r\n\r\n  // Search functionality removed as requested\r\n\r\n\r\n  const addItem = async () => {\r\n    if (!currentItem.description || !currentItem.hsn_code || currentItem.rate <= 0) {\r\n      showError('Please fill all item fields and ensure rate is positive');\r\n      return;\r\n    }\r\n\r\n    if (currentItem.quantity <= 0) {\r\n      showError('Quantity must be greater than zero');\r\n      return;\r\n    }\r\n\r\n    // Check if the product already exists in the database\r\n    let productExists = false;\r\n    let productId = currentItem.product_id;\r\n\r\n    // If no product_id, check if a product with the same description exists\r\n    if (!productId) {\r\n      const existingProduct = products.find(p =>\r\n        p.description.toLowerCase() === currentItem.description.toLowerCase()\r\n      );\r\n\r\n      if (existingProduct) {\r\n        productExists = true;\r\n        productId = existingProduct.id;\r\n        // Update current item with the existing product details\r\n        setCurrentItem(prev => ({\r\n          ...prev,\r\n          product_id: existingProduct.id,\r\n          hsn_code: existingProduct.hsn_code,\r\n          uom: existingProduct.uom,\r\n          rate: existingProduct.rate,\r\n          available_quantity: existingProduct.available_quantity\r\n        }));\r\n      } else {\r\n        // Create a new product with available_quantity = 0\r\n        try {\r\n          const newProduct = {\r\n            description: currentItem.description,\r\n            hsn_code: currentItem.hsn_code,\r\n            available_quantity: 0, // Set to 0 as requested\r\n            uom: currentItem.uom,\r\n            rate: currentItem.rate\r\n          };\r\n\r\n          const response = await axios.post('http://localhost:8000/products/', newProduct);\r\n          productId = response.data.id;\r\n\r\n          // Add the new product to the products array\r\n          setProducts(prev => [...prev, response.data]);\r\n\r\n          // Set the product_id in the current item\r\n          setCurrentItem(prev => ({\r\n            ...prev,\r\n            product_id: productId\r\n          }));\r\n\r\n          // Show success message\r\n          setNewProductAdded(currentItem.description);\r\n          setShowSuccessModal(true);\r\n\r\n        } catch (error) {\r\n          console.error('Error creating product:', error);\r\n          showError('Failed to create new product. Please try again.');\r\n          return;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Check if the product is already in the invoice\r\n    const existingItemIndex = formData.items.findIndex(item => item.description === currentItem.description);\r\n\r\n    if (existingItemIndex !== -1) {\r\n      // Update existing item instead of adding a new one\r\n      const updatedItems = [...formData.items];\r\n      const existingItem = updatedItems[existingItemIndex];\r\n\r\n      // Only check available quantity if it's an existing product with inventory\r\n      if (productExists && currentItem.available_quantity > 0) {\r\n        // Check if the combined quantity exceeds available quantity\r\n        const totalQuantity = existingItem.quantity + currentItem.quantity;\r\n        if (totalQuantity > currentItem.available_quantity) {\r\n          showError(`Cannot add ${currentItem.quantity} more units. Only ${currentItem.available_quantity - existingItem.quantity} units available.`);\r\n          return;\r\n        }\r\n      }\r\n\r\n      // Update the existing item\r\n      // Ensure all values are properly parsed as numbers\r\n      const rate = parseFloat(existingItem.rate) || 0;\r\n      const discount = parseFloat(existingItem.discount) || 0;\r\n      const existingQuantity = parseFloat(existingItem.quantity) || 0;\r\n      const additionalQuantity = parseFloat(currentItem.quantity) || 0;\r\n      const newQuantity = existingQuantity + additionalQuantity;\r\n\r\n      // Calculate taxable value with discount\r\n      const discountedRate = rate * (1 - discount/100);\r\n      const newTaxableValue = newQuantity * discountedRate;\r\n\r\n      // Ensure tax rates are properly parsed as numbers\r\n      const sgstRate = parseFloat(existingItem.sgst_rate) || 0;\r\n      const cgstRate = parseFloat(existingItem.cgst_rate) || 0;\r\n      const igstRate = parseFloat(existingItem.igst_rate) || 0;\r\n\r\n      // Calculate tax amounts\r\n      const sgstAmount = (newTaxableValue * sgstRate) / 100;\r\n      const cgstAmount = (newTaxableValue * cgstRate) / 100;\r\n      const igstAmount = (newTaxableValue * igstRate) / 100;\r\n\r\n      updatedItems[existingItemIndex] = {\r\n        ...existingItem,\r\n        rate: rate,\r\n        discount: discount,\r\n        quantity: newQuantity,\r\n        taxable_value: newTaxableValue,\r\n        sgst_rate: sgstRate,\r\n        cgst_rate: cgstRate,\r\n        igst_rate: igstRate,\r\n        sgst_amount: sgstAmount,\r\n        cgst_amount: cgstAmount,\r\n        igst_amount: igstAmount\r\n      };\r\n\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        items: updatedItems\r\n      }));\r\n    } else {\r\n      // Add as a new item\r\n      // Ensure all values are properly parsed as numbers\r\n      const quantity = parseFloat(currentItem.quantity) || 0;\r\n      const rate = parseFloat(currentItem.rate) || 0;\r\n      const discount = parseFloat(currentItem.discount) || 0;\r\n\r\n      // Calculate taxable value with discount\r\n      const discountedRate = rate * (1 - discount/100);\r\n      const taxableValue = quantity * discountedRate;\r\n\r\n      // Ensure tax rates are properly parsed as numbers\r\n      const sgstRate = parseFloat(currentItem.sgst_rate) || 0;\r\n      const cgstRate = parseFloat(currentItem.cgst_rate) || 0;\r\n      const igstRate = parseFloat(currentItem.igst_rate) || 0;\r\n\r\n      // Calculate tax amounts\r\n      const sgstAmount = (taxableValue * sgstRate) / 100;\r\n      const cgstAmount = (taxableValue * cgstRate) / 100;\r\n      const igstAmount = (taxableValue * igstRate) / 100;\r\n\r\n      const newItem = {\r\n        ...currentItem,\r\n        quantity: quantity,\r\n        rate: rate,\r\n        discount: discount,\r\n        taxable_value: taxableValue,\r\n        product_id: productId,\r\n        sgst_rate: sgstRate,\r\n        cgst_rate: cgstRate,\r\n        igst_rate: igstRate,\r\n        sgst_amount: sgstAmount,\r\n        cgst_amount: cgstAmount,\r\n        igst_amount: igstAmount\r\n      };\r\n\r\n      setFormData(prev => ({\r\n        ...prev,\r\n        items: [...prev.items, newItem]\r\n      }));\r\n    }\r\n\r\n    // Update the products array to reflect the reduced quantity (only for existing products)\r\n    if (productExists) {\r\n      const updatedProducts = products.map(product => {\r\n        if (product.id === productId) {\r\n          return {\r\n            ...product,\r\n            available_quantity: product.available_quantity - currentItem.quantity\r\n          };\r\n        }\r\n        return product;\r\n      });\r\n\r\n      setProducts(updatedProducts);\r\n    }\r\n\r\n    // Reset current item\r\n    setCurrentItem({\r\n      sr_no: formData.items.length + 2,\r\n      description: '',\r\n      hsn_code: '',\r\n      quantity: 1,\r\n      uom: 'NOS',\r\n      rate: 0,\r\n      discount: 0, // Explicitly set to 0 (number)\r\n      sgst_rate: 0,\r\n      cgst_rate: 0,\r\n      igst_rate: 0,\r\n      product_id: null,\r\n      available_quantity: 0,\r\n      taxable_value: 0, // Initialize taxable_value to 0\r\n      sgst_amount: 0, // Initialize tax amounts to 0\r\n      cgst_amount: 0,\r\n      igst_amount: 0\r\n    });\r\n  };\r\n\r\n  const removeItem = (index) => {\r\n    const removedItem = formData.items[index];\r\n    const newItems = [...formData.items];\r\n    newItems.splice(index, 1);\r\n\r\n    // Update serial numbers\r\n    const updatedItems = newItems.map((item, idx) => ({\r\n      ...item,\r\n      sr_no: idx + 1\r\n    }));\r\n\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      items: updatedItems\r\n    }));\r\n\r\n    // Restore the product quantity\r\n    const updatedProducts = products.map(product => {\r\n      if (product.description === removedItem.description) {\r\n        return {\r\n          ...product,\r\n          available_quantity: product.available_quantity + removedItem.quantity\r\n        };\r\n      }\r\n      return product;\r\n    });\r\n\r\n    setProducts(updatedProducts);\r\n\r\n    setCurrentItem(prev => ({\r\n      ...prev,\r\n      sr_no: updatedItems.length + 1\r\n    }));\r\n  };\r\n\r\n  const calculateTotals = () => {\r\n    // Recalculate all tax amounts for each item to ensure they're correct\r\n    const recalculatedItems = formData.items.map(item => {\r\n      // Ensure all values are properly parsed as numbers\r\n      const quantity = parseFloat(item.quantity) || 0;\r\n      const rate = parseFloat(item.rate) || 0;\r\n      const discount = parseFloat(item.discount) || 0;\r\n      const sgstRate = parseFloat(item.sgst_rate) || 0;\r\n      const cgstRate = parseFloat(item.cgst_rate) || 0;\r\n      const igstRate = parseFloat(item.igst_rate) || 0;\r\n\r\n      // Calculate taxable value\r\n      const discountedRate = rate * (1 - discount/100);\r\n      const taxableValue = quantity * discountedRate;\r\n\r\n      // Calculate tax amounts\r\n      const sgstAmount = (taxableValue * sgstRate) / 100;\r\n      const cgstAmount = (taxableValue * cgstRate) / 100;\r\n      const igstAmount = (taxableValue * igstRate) / 100;\r\n\r\n      return {\r\n        ...item,\r\n        taxable_value: taxableValue,\r\n        sgst_amount: sgstAmount,\r\n        cgst_amount: cgstAmount,\r\n        igst_amount: igstAmount\r\n      };\r\n    });\r\n\r\n    // Calculate item-level totals using the recalculated values\r\n    const taxableValue = recalculatedItems.reduce((sum, item) => sum + item.taxable_value, 0);\r\n    const itemSgstAmount = recalculatedItems.reduce((sum, item) => sum + item.sgst_amount, 0);\r\n    const itemCgstAmount = recalculatedItems.reduce((sum, item) => sum + item.cgst_amount, 0);\r\n    const itemIgstAmount = recalculatedItems.reduce((sum, item) => sum + item.igst_amount, 0);\r\n\r\n    // Get freight & forwarding amount\r\n    const freightForwarding = parseFloat(formData.freight_forwarding || 0);\r\n\r\n    // Calculate total taxable amount (taxable value + freight & forwarding)\r\n    const totalTaxableAmount = taxableValue + freightForwarding;\r\n\r\n    // Determine if we should use CGST+SGST or IGST for freight & forwarding\r\n    // based on customer and company state codes\r\n    let ffSgstAmount = 0;\r\n    let ffCgstAmount = 0;\r\n    let ffIgstAmount = 0;\r\n\r\n    if (freightForwarding > 0 && formData.customer_id && companyInfo) {\r\n      const selectedCustomer = customers.find(c => c.id === parseInt(formData.customer_id));\r\n      if (selectedCustomer) {\r\n        const isSameState = selectedCustomer.state_code === companyInfo.state_code;\r\n\r\n        if (isSameState) {\r\n          // Apply CGST (9%) + SGST (9%) for same state\r\n          ffSgstAmount = (freightForwarding * 9) / 100;\r\n          ffCgstAmount = (freightForwarding * 9) / 100;\r\n        } else {\r\n          // Apply IGST (18%) for different states\r\n          ffIgstAmount = (freightForwarding * 18) / 100;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Calculate total tax amounts (items + freight & forwarding)\r\n    const totalSgst = itemSgstAmount + ffSgstAmount;\r\n    const totalCgst = itemCgstAmount + ffCgstAmount;\r\n    const totalIgst = itemIgstAmount + ffIgstAmount;\r\n\r\n    // Calculate grand total\r\n    const grandTotal = totalTaxableAmount + totalSgst + totalCgst + totalIgst;\r\n\r\n    // Calculate effective tax rates for display\r\n    let totalSgstRate = 0;\r\n    let totalCgstRate = 0;\r\n    let totalIgstRate = 0;\r\n\r\n    if (totalTaxableAmount > 0) {\r\n      totalSgstRate = (totalSgst / totalTaxableAmount) * 100;\r\n      totalCgstRate = (totalCgst / totalTaxableAmount) * 100;\r\n      totalIgstRate = (totalIgst / totalTaxableAmount) * 100;\r\n    }\r\n\r\n    return {\r\n      taxableValue,\r\n      freightForwarding,\r\n      totalTaxableAmount,\r\n      sgst: totalSgst,\r\n      cgst: totalCgst,\r\n      igst: totalIgst,\r\n      totalSgstRate,\r\n      totalCgstRate,\r\n      totalIgstRate,\r\n      grandTotal,\r\n      // Additional details for debugging\r\n      itemSgstAmount,\r\n      itemCgstAmount,\r\n      itemIgstAmount,\r\n      ffSgstAmount,\r\n      ffCgstAmount,\r\n      ffIgstAmount\r\n    };\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    // Check if at least one item has been added\r\n    if (formData.items.length === 0) {\r\n      showError('Please add at least one item to the invoice');\r\n      return;\r\n    }\r\n    try {\r\n      setLoading(true);\r\n\r\n      // Log the form data being sent\r\n      console.log('Submitting invoice data:', formData);\r\n\r\n      // Create a copy of the form data to modify\r\n      const dataToSend = {\r\n        ...formData,\r\n        freight_forwarding: parseFloat(formData.freight_forwarding) || 0,\r\n        customer_id: parseInt(formData.customer_id, 10),\r\n        // Include consignee details\r\n        consignee_name: formData.consignee_name,\r\n        consignee_address: formData.consignee_address,\r\n        consignee_state: formData.consignee_state,\r\n        consignee_state_code: formData.consignee_state_code,\r\n        consignee_gstin: formData.consignee_gstin,\r\n        items: formData.items.map(item => ({\r\n          ...item,\r\n          quantity: parseFloat(item.quantity),\r\n          rate: parseFloat(item.rate)\r\n        }))\r\n      };\r\n\r\n      // Handle the invoice_date format\r\n      if (dataToSend.invoice_date) {\r\n        const invoiceDateObj = new Date(dataToSend.invoice_date);\r\n        dataToSend.invoice_date = invoiceDateObj.toISOString();\r\n      }\r\n\r\n      // Handle the po_date format - if it's empty, don't send it\r\n      if (!dataToSend.po_date) {\r\n        delete dataToSend.po_date;\r\n      } else {\r\n        // Ensure the date is in ISO format (YYYY-MM-DDTHH:mm:ss.sssZ)\r\n        const poDateObj = new Date(dataToSend.po_date);\r\n        dataToSend.po_date = poDateObj.toISOString();\r\n      }\r\n\r\n      const response = await axios.post('http://localhost:8000/invoices/', dataToSend);\r\n      console.log('Invoice created successfully:', response.data);\r\n\r\n      showSuccess('Invoice created successfully!');\r\n      navigate('/invoices'); // Changed from navigation.navigate to navigate\r\n    } catch (error) {\r\n      console.error('Error creating invoice:', error.response?.data || error.message);\r\n\r\n      // Check if it's a subscription limit error\r\n      if (error.response?.status === 403 && error.response?.data?.detail?.includes('Invoice limit reached')) {\r\n        showError(\r\n          <div>\r\n            {error.response.data.detail}\r\n            <br />\r\n            <a href=\"/subscription\" target=\"_blank\" rel=\"noopener noreferrer\" style={{ color: '#007bff', textDecoration: 'underline' }}>\r\n              Upgrade your subscription\r\n            </a>\r\n          </div>\r\n        );\r\n      } else {\r\n        showError('Failed to create invoice');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const totals = calculateTotals();\r\n\r\n  // Add this code right before the return statement in the component\r\n  // This will help us see what values are being used in the condition\r\n  useEffect(() => {\r\n    if (formData.customer_id && companyInfo) {\r\n      const customer = customers.find(c => c.id === parseInt(formData.customer_id));\r\n      console.log('Customer state code:', customer?.state_code);\r\n      console.log('Company state code:', companyInfo?.state_code);\r\n      console.log('Are they equal?', customer?.state_code === companyInfo?.state_code);\r\n    }\r\n  }, [formData.customer_id, companyInfo, customers]);\r\n\r\n  // Add debugging for tax calculations\r\n  useEffect(() => {\r\n    if (formData.items.length > 0) {\r\n      console.log('Items with tax details:');\r\n      formData.items.forEach((item, index) => {\r\n        console.log(`Item ${index + 1}: ${item.description}`);\r\n        console.log(`  Taxable Value: ${item.taxable_value}`);\r\n        console.log(`  SGST Rate: ${item.sgst_rate}%, Amount: ${item.sgst_amount}`);\r\n        console.log(`  CGST Rate: ${item.cgst_rate}%, Amount: ${item.cgst_amount}`);\r\n        console.log(`  IGST Rate: ${item.igst_rate}%, Amount: ${item.igst_amount}`);\r\n      });\r\n      console.log('Total tax amounts:');\r\n      console.log(`  Total SGST: ${totals.sgst}`);\r\n      console.log(`  Total CGST: ${totals.cgst}`);\r\n      console.log(`  Total IGST: ${totals.igst}`);\r\n      console.log(`  Grand Total: ${totals.grandTotal}`);\r\n    }\r\n  }, [formData.items, totals]);\r\n\r\n\r\n  return (\r\n    <div className=\"invoices-container\">\r\n      <div className=\"invoices-header\">\r\n        <div>\r\n          <h2 className=\"invoices-title\">Create New Invoice</h2>\r\n          <p className=\"invoices-subtitle\">Create a new sales invoice for your customers</p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Profile Completion Warning */}\r\n      {profileCompletion && !profileCompletion.profile_completed && (\r\n        <Alert variant=\"warning\" className=\"mb-4\">\r\n          <Alert.Heading>\r\n            <i className=\"fas fa-exclamation-triangle me-2\"></i>\r\n            Complete Your Profile\r\n          </Alert.Heading>\r\n          <p className=\"mb-3\">\r\n            You need to complete your profile before creating invoices. Please fill in the following required sections:\r\n          </p>\r\n          <Row>\r\n            <Col md={6}>\r\n              <h6>Personal Info:</h6>\r\n              <ul className=\"mb-0\">\r\n                <li className={profileCompletion.required_for_invoice?.personal_info?.company_name ? 'text-success' : 'text-danger'}>\r\n                  <i className={`fas ${profileCompletion.required_for_invoice?.personal_info?.company_name ? 'fa-check' : 'fa-times'} me-1`}></i>\r\n                  Company Name\r\n                </li>\r\n                <li className={profileCompletion.required_for_invoice?.personal_info?.mobile_number ? 'text-success' : 'text-danger'}>\r\n                  <i className={`fas ${profileCompletion.required_for_invoice?.personal_info?.mobile_number ? 'fa-check' : 'fa-times'} me-1`}></i>\r\n                  Mobile Number\r\n                </li>\r\n                <li className={profileCompletion.required_for_invoice?.personal_info?.email ? 'text-success' : 'text-danger'}>\r\n                  <i className={`fas ${profileCompletion.required_for_invoice?.personal_info?.email ? 'fa-check' : 'fa-times'} me-1`}></i>\r\n                  Email\r\n                </li>\r\n              </ul>\r\n            </Col>\r\n            <Col md={6}>\r\n              <h6>Company Details:</h6>\r\n              <ul className=\"mb-0\">\r\n                <li className={profileCompletion.required_for_invoice?.company_details?.address ? 'text-success' : 'text-danger'}>\r\n                  <i className={`fas ${profileCompletion.required_for_invoice?.company_details?.address ? 'fa-check' : 'fa-times'} me-1`}></i>\r\n                  Address\r\n                </li>\r\n                <li className={profileCompletion.required_for_invoice?.company_details?.state ? 'text-success' : 'text-danger'}>\r\n                  <i className={`fas ${profileCompletion.required_for_invoice?.company_details?.state ? 'fa-check' : 'fa-times'} me-1`}></i>\r\n                  State\r\n                </li>\r\n                <li className={profileCompletion.required_for_invoice?.company_details?.gstin ? 'text-success' : 'text-danger'}>\r\n                  <i className={`fas ${profileCompletion.required_for_invoice?.company_details?.gstin ? 'fa-check' : 'fa-times'} me-1`}></i>\r\n                  GSTIN\r\n                </li>\r\n              </ul>\r\n            </Col>\r\n          </Row>\r\n          <hr />\r\n          <div className=\"d-flex justify-content-between align-items-center\">\r\n            <span>Profile Completion: <strong>{profileCompletion.profile_completion_percentage}%</strong></span>\r\n            <Button\r\n              variant=\"primary\"\r\n              size=\"sm\"\r\n              onClick={() => window.location.href = '/dashboard?section=profile'}\r\n            >\r\n              <i className=\"fas fa-user-edit me-1\"></i>\r\n              Complete Profile\r\n            </Button>\r\n          </div>\r\n        </Alert>\r\n      )}\r\n\r\n      <Form onSubmit={handleSubmit}>\r\n        <Card className=\"invoice-card mb-4\">\r\n          <Card.Header>\r\n            <h4><i className=\"fas fa-user-tie\"></i> Customer Details</h4>\r\n          </Card.Header>\r\n          <Card.Body>\r\n            <Row className=\"mb-4\">\r\n              <Col md={4}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"invoice-form-label\">Invoice Date</Form.Label>\r\n                  <DatePicker\r\n                    selected={formData.invoice_date ? new Date(formData.invoice_date) : new Date()}\r\n                    onChange={(date) => setFormData(prev => ({ ...prev, invoice_date: date ? date.toISOString().split('T')[0] : new Date().toISOString().split('T')[0] }))}\r\n                    className=\"form-control invoice-form-control\"\r\n                    dateFormat=\"dd/MM/yyyy\"\r\n                    portalId=\"root-portal\"\r\n                    popperPlacement=\"bottom-start\"\r\n                    required\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={4}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"invoice-form-label\">PO Number</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"po_number\"\r\n                    value={formData.po_number}\r\n                    onChange={handleChange}\r\n                    className=\"invoice-form-control\"\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={4}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"invoice-form-label\">PO Date</Form.Label>\r\n                  <DatePicker\r\n                    selected={formData.po_date ? new Date(formData.po_date) : null}\r\n                    onChange={(date) => setFormData(prev => ({ ...prev, po_date: date ? date.toISOString().split('T')[0] : '' }))}\r\n                    className=\"form-control invoice-form-control\"\r\n                    dateFormat=\"dd/MM/yyyy\"\r\n                    portalId=\"root-portal\"\r\n                    popperPlacement=\"bottom-start\"\r\n                    isClearable\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            {/* Buyer (Bill To) Section */}\r\n            <Card className=\"invoice-card mb-4\">\r\n              <Card.Header>\r\n                <h4><i className=\"fas fa-file-invoice\"></i> Buyer (Bill To)</h4>\r\n              </Card.Header>\r\n              <Card.Body>\r\n              <Row>\r\n                <Col md={12}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label className=\"invoice-form-label\">Select Customer</Form.Label>\r\n                    <Form.Select\r\n                      name=\"customer_id\"\r\n                      required\r\n                      value={formData.customer_id}\r\n                      onChange={handleChange}\r\n                      className=\"invoice-form-select\"\r\n                    >\r\n                      <option value=\"\">Select Customer</option>\r\n                      {customers.map(customer => (\r\n                        <option key={customer.id} value={customer.id}>\r\n                          {customer.name}\r\n                        </option>\r\n                      ))}\r\n                    </Form.Select>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              {formData.customer_id && (\r\n                <div className=\"selected-customer-details p-3 mb-3 border rounded bg-light\">\r\n                  {(() => {\r\n                    const customer = customers.find(c => c.id === parseInt(formData.customer_id));\r\n                    return customer ? (\r\n                      <>\r\n                        <p className=\"mb-1\"><strong>Name:</strong> {customer.name}</p>\r\n                        <p className=\"mb-1\"><strong>Address:</strong> {customer.address}</p>\r\n                        <p className=\"mb-1\"><strong>State:</strong> {customer.state} ({customer.state_code})</p>\r\n                        <p className=\"mb-1\"><strong>GSTIN:</strong> {customer.gstin}</p>\r\n                        <p className=\"mb-0\"><strong>Mobile:</strong> {customer.mobile_number}</p>\r\n                      </>\r\n                    ) : null;\r\n                  })()}\r\n                </div>\r\n              )}\r\n              </Card.Body>\r\n            </Card>\r\n\r\n            {/* Consignee (Ship To) Section */}\r\n            <Card className=\"invoice-card mb-4\">\r\n              <Card.Header>\r\n                <h4><i className=\"fas fa-shipping-fast\"></i> Consignee (Ship To)</h4>\r\n              </Card.Header>\r\n              <Card.Body>\r\n\r\n              <Form.Check\r\n                type=\"checkbox\"\r\n                id=\"same-as-buyer\"\r\n                label=\"Same as Buyer\"\r\n                checked={sameAsBuyer}\r\n                onChange={handleSameAsBuyer}\r\n                className=\"mb-3\"\r\n              />\r\n\r\n              {!sameAsBuyer && (\r\n                <Row>\r\n                  <Col md={12}>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label className=\"invoice-form-label\">Select Customer</Form.Label>\r\n                      <Form.Select\r\n                        name=\"consignee_customer_id\"\r\n                        onChange={handleChange}\r\n                        className=\"invoice-form-select\"\r\n                      >\r\n                        <option value=\"\">Select Customer</option>\r\n                        {customers.map(customer => (\r\n                          <option key={customer.id} value={customer.id}>\r\n                            {customer.name}\r\n                          </option>\r\n                        ))}\r\n                      </Form.Select>\r\n                      <Form.Text className=\"text-muted\">\r\n                        Select a customer to auto-fill details or enter manually below\r\n                      </Form.Text>\r\n                    </Form.Group>\r\n                  </Col>\r\n                </Row>\r\n              )}\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label className=\"invoice-form-label\">Name</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"consignee_name\"\r\n                      value={formData.consignee_name}\r\n                      onChange={handleChange}\r\n                      className=\"invoice-form-control\"\r\n                      placeholder=\"Enter consignee name\"\r\n                      required={!sameAsBuyer}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label className=\"invoice-form-label\">GSTIN</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"consignee_gstin\"\r\n                      value={formData.consignee_gstin}\r\n                      onChange={handleChange}\r\n                      className=\"invoice-form-control\"\r\n                      placeholder=\"Enter GSTIN\"\r\n                      required={!sameAsBuyer}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={12}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label className=\"invoice-form-label\">Address</Form.Label>\r\n                    <Form.Control\r\n                      as=\"textarea\"\r\n                      rows={3}\r\n                      name=\"consignee_address\"\r\n                      value={formData.consignee_address}\r\n                      onChange={handleChange}\r\n                      className=\"invoice-form-control\"\r\n                      placeholder=\"Enter shipping address\"\r\n                      required={!sameAsBuyer}\r\n                    />\r\n                    <Form.Text className=\"text-muted\">\r\n                      Always enter the shipping address manually, even when selecting from dropdown\r\n                    </Form.Text>\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n\r\n              <Row>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label className=\"invoice-form-label\">State</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"consignee_state\"\r\n                      value={formData.consignee_state}\r\n                      onChange={handleChange}\r\n                      className=\"invoice-form-control\"\r\n                      placeholder=\"Enter state\"\r\n                      required={!sameAsBuyer}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n                <Col md={6}>\r\n                  <Form.Group className=\"mb-3\">\r\n                    <Form.Label className=\"invoice-form-label\">State Code</Form.Label>\r\n                    <Form.Control\r\n                      type=\"text\"\r\n                      name=\"consignee_state_code\"\r\n                      value={formData.consignee_state_code}\r\n                      onChange={handleChange}\r\n                      className=\"invoice-form-control\"\r\n                      placeholder=\"Enter state code\"\r\n                      required={!sameAsBuyer}\r\n                    />\r\n                  </Form.Group>\r\n                </Col>\r\n              </Row>\r\n              </Card.Body>\r\n            </Card>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"invoice-form-label\">Reverse Charge</Form.Label>\r\n                  <Form.Select\r\n                    name=\"reverse_charge\"\r\n                    value={formData.reverse_charge}\r\n                    onChange={handleChange}\r\n                    className=\"invoice-form-select\"\r\n                  >\r\n                    <option value=\"No\">No</option>\r\n                    <option value=\"Yes\">Yes</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"invoice-form-label\">Freight & Forwarding</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"freight_forwarding\"\r\n                    min=\"0\"\r\n                    step=\"0.01\"\r\n                    value={formData.freight_forwarding}\r\n                    onChange={handleChange}\r\n                    className=\"invoice-form-control\"\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"invoice-form-label\">Vehicle Number</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"vehicle_number\"\r\n                    value={formData.vehicle_number}\r\n                    onChange={handleChange}\r\n                    className=\"invoice-form-control\"\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={6}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label className=\"invoice-form-label\">Transporter Name</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"transporter_name\"\r\n                    value={formData.transporter_name}\r\n                    onChange={handleChange}\r\n                    className=\"invoice-form-control\"\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n          </Card.Body>\r\n        </Card>\r\n\r\n        <Card className=\"invoice-card mb-4\">\r\n          <Card.Header>\r\n            <h4><i className=\"fas fa-shopping-cart\"></i> Items</h4>\r\n          </Card.Header>\r\n          <Card.Body>\r\n\r\n            <Row>\r\n              <Col md={12}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Select Product</Form.Label>\r\n                  <Form.Select\r\n                    onChange={(e) => {\r\n                      const product = products.find(p => p.id === parseInt(e.target.value, 10));\r\n                      if (product) handleProductSelect(product);\r\n                    }}\r\n                  >\r\n                    <option value=\"\">Select Product</option>\r\n                    {products.map(product => (\r\n                      <option\r\n                        key={product.id}\r\n                        value={product.id}\r\n                        disabled={product.available_quantity <= 0}\r\n                      >\r\n                        {product.description} - ₹{product.rate.toFixed(2)} - {product.available_quantity} available\r\n                        {product.available_quantity <= 0 ? ' (Out of Stock)' : ''}\r\n                      </option>\r\n                    ))}\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            <Row className=\"align-items-end\">\r\n              <Col md={2}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Description</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"description\"\r\n                    value={currentItem.description}\r\n                    onChange={handleItemChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>HSN/SAC</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"hsn_code\"\r\n                    value={currentItem.hsn_code}\r\n                    onChange={handleItemChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Qty</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"quantity\"\r\n                    min=\"1\"\r\n                    step=\"1\"\r\n                    value={currentItem.quantity}\r\n                    onChange={handleItemChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>UOM</Form.Label>\r\n                  <Form.Control\r\n                    type=\"text\"\r\n                    name=\"uom\"\r\n                    value={currentItem.uom}\r\n                    onChange={handleItemChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Rate</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"rate\"\r\n                    min=\"0\"\r\n                    step=\"0.01\"\r\n                    value={currentItem.rate}\r\n                    onChange={handleItemChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>Discount (%)</Form.Label>\r\n                  <Form.Control\r\n                    type=\"number\"\r\n                    name=\"discount\"\r\n                    min=\"0\"\r\n                    max=\"100\"\r\n                    step=\"1\"\r\n                    value={currentItem.discount}\r\n                    onChange={handleItemChange}\r\n                  />\r\n                </Form.Group>\r\n              </Col>\r\n              <Col>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>SGST+CGST</Form.Label>\r\n                  <Form.Select\r\n                    name=\"sgst_cgst_rate\"\r\n                    value={`${currentItem.sgst_rate}+${currentItem.cgst_rate}`}\r\n                    onChange={(e) => {\r\n                      const [sgst, cgst] = e.target.value.split('+').map(parseFloat);\r\n                      setCurrentItem(prev => ({\r\n                        ...prev,\r\n                        sgst_rate: sgst || 0,\r\n                        cgst_rate: cgst || 0,\r\n                        igst_rate: 0 // Reset IGST when SGST+CGST is selected\r\n                      }));\r\n                    }}\r\n                    disabled={\r\n                      !formData.customer_id ||\r\n                      !companyInfo ||\r\n                      (formData.customer_id && customers.find(c => c.id === parseInt(formData.customer_id))?.state_code !== companyInfo?.state_code)\r\n                    }\r\n                  >\r\n                    <option value=\"0+0\">-Select-</option>\r\n                    <option value=\"0.500+0.500\">0.500 + 0.500</option>\r\n                    <option value=\"0.750+0.750\">0.750 + 0.750</option>\r\n                    <option value=\"3.750+3.750\">3.750 + 3.750</option>\r\n                    <option value=\"0+0\">0 + 0</option>\r\n                    <option value=\"2.500+2.500\">2.500 + 2.500</option>\r\n                    <option value=\"6+6\">6 + 6</option>\r\n                    <option value=\"9+9\">9 + 9</option>\r\n                    <option value=\"14+14\">14 + 14</option>\r\n                    <option value=\"1.500+1.500\">1.500 + 1.500</option>\r\n                    <option value=\"0.050+0.050\">0.050 + 0.050</option>\r\n                    <option value=\"0.125+0.125\">0.125 + 0.125</option>\r\n                    <option value=\"3+3\">3 + 3</option>\r\n                    <option value=\"0+0\">Not Appl.</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n              <Col>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>IGST</Form.Label>\r\n                  <Form.Select\r\n                    name=\"igst_rate\"\r\n                    value={currentItem.igst_rate}\r\n                    onChange={(e) => {\r\n                      const igstRate = parseFloat(e.target.value) || 0;\r\n                      setCurrentItem(prev => ({\r\n                        ...prev,\r\n                        igst_rate: igstRate,\r\n                        sgst_rate: 0, // Reset SGST when IGST is selected\r\n                        cgst_rate: 0  // Reset CGST when IGST is selected\r\n                      }));\r\n                    }}\r\n                    disabled={\r\n                      !formData.customer_id ||\r\n                      !companyInfo ||\r\n                      (formData.customer_id && customers.find(c => c.id === parseInt(formData.customer_id))?.state_code === companyInfo?.state_code)\r\n                    }\r\n                  >\r\n                    <option value=\"0\">-Select-</option>\r\n                    <option value=\"1\">1</option>\r\n                    <option value=\"1.500\">1.500</option>\r\n                    <option value=\"7.500\">7.500</option>\r\n                    <option value=\"0\">0</option>\r\n                    <option value=\"5\">5</option>\r\n                    <option value=\"12\">12</option>\r\n                    <option value=\"18\">18</option>\r\n                    <option value=\"28\">28</option>\r\n                    <option value=\"3\">3</option>\r\n                    <option value=\"0.100\">0.100</option>\r\n                    <option value=\"0.250\">0.250</option>\r\n                    <option value=\"6\">6</option>\r\n                    <option value=\"0\">Not Appl.</option>\r\n                  </Form.Select>\r\n                </Form.Group>\r\n              </Col>\r\n              <Col md={1}>\r\n                <Form.Group className=\"mb-3\">\r\n                  <Form.Label>&nbsp;</Form.Label>\r\n                  <div>\r\n                    <Button\r\n                      className=\"invoice-btn-icon invoice-btn-primary\"\r\n                      onClick={addItem}\r\n                      title=\"Add Item\"\r\n                    >\r\n                      <i className=\"fas fa-plus\"></i>\r\n                    </Button>\r\n                  </div>\r\n                </Form.Group>\r\n              </Col>\r\n            </Row>\r\n\r\n            {formData.items.length > 0 && (\r\n              <div className=\"table-responsive\">\r\n              <Table className=\"items-table mt-4\">\r\n                <thead>\r\n                  <tr>\r\n                    <th>Sr. No.</th>\r\n                    <th>Description</th>\r\n                    <th>HSN/SAC Code</th>\r\n                    <th>Qty</th>\r\n                    <th>UOM</th>\r\n                    <th>Rate</th>\r\n                    <th>Discount</th>\r\n                    <th>CGST+SGST</th>\r\n                    <th>IGST</th>\r\n                    <th>Amount</th>\r\n                    <th>Action</th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody>\r\n                  {formData.items.map((item, index) => (\r\n                    <tr key={index}>\r\n                      <td>{item.sr_no}</td>\r\n                      <td>{item.description}</td>\r\n                      <td>{item.hsn_code}</td>\r\n                      <td>{item.quantity}</td>\r\n                      <td>{item.uom}</td>\r\n                      <td>{item.rate.toFixed(2)}</td>\r\n                      <td>{item.discount > 0 ? `${item.discount}%` : '-'}</td>\r\n                      <td>{item.sgst_rate > 0 ? `${item.sgst_rate}% + ${item.cgst_rate}%` : '-'}</td>\r\n                      <td>{item.igst_rate > 0 ? `${item.igst_rate}%` : '-'}</td>\r\n                      <td>{item.taxable_value.toFixed(2)}</td>\r\n                      <td>\r\n                        <Button\r\n                          className=\"invoice-btn invoice-btn-danger invoice-btn-sm\"\r\n                          onClick={() => removeItem(index)}\r\n                        >\r\n                          <i className=\"fas fa-trash me-1\"></i> Remove\r\n                        </Button>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </Table>\r\n              </div>\r\n            )}\r\n          </Card.Body>\r\n        </Card>\r\n\r\n        <Card className=\"invoice-card mb-4\">\r\n          <Card.Header>\r\n            <h4><i className=\"fas fa-calculator\"></i> Invoice Totals</h4>\r\n          </Card.Header>\r\n          <Card.Body>\r\n\r\n            <div className=\"invoice-totals\">\r\n              <div className=\"invoice-totals-row\">\r\n                <div className=\"invoice-totals-label\">TOTAL TAXABLE VALUE</div>\r\n                <div className=\"invoice-totals-value\">₹{totals.taxableValue.toFixed(2)}</div>\r\n              </div>\r\n              <div className=\"invoice-totals-row\">\r\n                <div className=\"invoice-totals-label\">ADD: FREIGHT & FORWARDING</div>\r\n                <div className=\"invoice-totals-value\">₹{totals.freightForwarding.toFixed(2)}</div>\r\n              </div>\r\n              <div className=\"invoice-totals-row\">\r\n                <div className=\"invoice-totals-label\">TOTAL TAXABLE AMOUNT</div>\r\n                <div className=\"invoice-totals-value\">₹{totals.totalTaxableAmount.toFixed(2)}</div>\r\n              </div>\r\n\r\n              {/* SGST Details */}\r\n              {totals.sgst > 0 && (\r\n                <div className=\"invoice-totals-row\">\r\n                  <div className=\"invoice-totals-label\">\r\n                    ADD: SGST\r\n                    {totals.itemSgstAmount > 0 && totals.ffSgstAmount > 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (Items: ₹{totals.itemSgstAmount.toFixed(2)}, F&F: ₹{totals.ffSgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                    {totals.itemSgstAmount > 0 && totals.ffSgstAmount === 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (Items: ₹{totals.itemSgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                    {totals.itemSgstAmount === 0 && totals.ffSgstAmount > 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (F&F: ₹{totals.ffSgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"invoice-totals-value\">₹{totals.sgst.toFixed(2)}</div>\r\n                </div>\r\n              )}\r\n\r\n              {/* CGST Details */}\r\n              {totals.cgst > 0 && (\r\n                <div className=\"invoice-totals-row\">\r\n                  <div className=\"invoice-totals-label\">\r\n                    ADD: CGST\r\n                    {totals.itemCgstAmount > 0 && totals.ffCgstAmount > 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (Items: ₹{totals.itemCgstAmount.toFixed(2)}, F&F: ₹{totals.ffCgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                    {totals.itemCgstAmount > 0 && totals.ffCgstAmount === 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (Items: ₹{totals.itemCgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                    {totals.itemCgstAmount === 0 && totals.ffCgstAmount > 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (F&F: ₹{totals.ffCgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"invoice-totals-value\">₹{totals.cgst.toFixed(2)}</div>\r\n                </div>\r\n              )}\r\n\r\n              {/* IGST Details */}\r\n              {totals.igst > 0 && (\r\n                <div className=\"invoice-totals-row\">\r\n                  <div className=\"invoice-totals-label\">\r\n                    ADD: IGST\r\n                    {totals.itemIgstAmount > 0 && totals.ffIgstAmount > 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (Items: ₹{totals.itemIgstAmount.toFixed(2)}, F&F: ₹{totals.ffIgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                    {totals.itemIgstAmount > 0 && totals.ffIgstAmount === 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (Items: ₹{totals.itemIgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                    {totals.itemIgstAmount === 0 && totals.ffIgstAmount > 0 && (\r\n                      <small className=\"d-block text-muted\">\r\n                        (F&F: ₹{totals.ffIgstAmount.toFixed(2)})\r\n                      </small>\r\n                    )}\r\n                  </div>\r\n                  <div className=\"invoice-totals-value\">₹{totals.igst.toFixed(2)}</div>\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"invoice-totals-row font-weight-bold\">\r\n                <div className=\"invoice-totals-label\">INVOICE GRAND TOTAL</div>\r\n                <div className=\"invoice-totals-value\">₹{totals.grandTotal.toFixed(2)}</div>\r\n              </div>\r\n            </div>\r\n          </Card.Body>\r\n        </Card>\r\n\r\n        <div className=\"d-flex justify-content-between\" style={{ marginBottom: \"0.5in\" }}>\r\n          <Button\r\n            className=\"invoice-btn\"\r\n            onClick={() => navigate('/invoices')}\r\n            disabled={loading}\r\n          >\r\n            <i className=\"fas fa-times me-1\"></i> Cancel\r\n          </Button>\r\n          <Button\r\n            className=\"invoice-btn invoice-btn-primary\"\r\n            type=\"submit\"\r\n            disabled={loading || formData.items.length === 0}\r\n          >\r\n            {loading ? (\r\n              <>\r\n                <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                Creating Invoice...\r\n              </>\r\n            ) : (\r\n              <><i className=\"fas fa-save me-1\"></i> Create Invoice</>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </Form>\r\n\r\n      {/* Success Modal for New Product */}\r\n      <Modal\r\n        show={showSuccessModal}\r\n        onHide={() => setShowSuccessModal(false)}\r\n        centered\r\n        className=\"product-success-modal\"\r\n      >\r\n        <Modal.Header closeButton className=\"bg-success text-white\">\r\n          <Modal.Title><i className=\"fas fa-check-circle me-2\"></i>Product Added Successfully</Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body className=\"py-4\">\r\n          <div className=\"text-center mb-3\">\r\n            <div className=\"success-icon-container mb-3\">\r\n              <i className=\"fas fa-box-open fa-3x text-success\"></i>\r\n            </div>\r\n            <h5 className=\"mb-3\">Product \"{newProductAdded}\" has been successfully added to your product list with available quantity 0.</h5>\r\n            <p className=\"text-muted\">You can manage this product later in the Products section.</p>\r\n          </div>\r\n        </Modal.Body>\r\n        <Modal.Footer className=\"border-0 justify-content-center\">\r\n          <Button\r\n            variant=\"success\"\r\n            size=\"lg\"\r\n            className=\"px-4\"\r\n            onClick={() => setShowSuccessModal(false)}\r\n          >\r\n            Continue\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,QAAQ,iBAAiB;AAC5E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAO,4CAA4C;AACnD,OAAO,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEnC,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,eAAA,EAAAC,gBAAA;EACtC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACgD,QAAQ,EAAEC,WAAW,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EAChE;;EAEA,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC;IACvCwD,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAAE;IACtDC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE,EAAE;IACXC,cAAc,EAAE,IAAI;IACpBC,kBAAkB,EAAE,CAAC;IACrBC,cAAc,EAAE,EAAE;IAClBC,gBAAgB,EAAE,EAAE;IACpB;IACAC,cAAc,EAAE,EAAE;IAClBC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBC,oBAAoB,EAAE,EAAE;IACxBC,eAAe,EAAE,EAAE;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC;IAC7C6E,KAAK,EAAE,CAAC;IACRC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE,CAAC;IACXC,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,CAAC;IAAE;IACbC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE,IAAI;IAChBC,kBAAkB,EAAE,CAAC;IACrBC,aAAa,EAAE,CAAC;IAAE;IAClBC,WAAW,EAAE,CAAC;IAAE;IAChBC,WAAW,EAAE,CAAC;IAAE;IAChBC,WAAW,EAAE,CAAC,CAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9F,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+F,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACiG,eAAe,EAAEC,kBAAkB,CAAC,GAAGlG,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM;IAAEmG;EAAY,CAAC,GAAGzF,OAAO,CAAC,CAAC;EACjC,MAAM;IAAE0F,SAAS;IAAEC,WAAW;IAAEC;EAAY,CAAC,GAAG3F,QAAQ,CAAC,CAAC;EAC1D,MAAM4F,QAAQ,GAAG3F,WAAW,CAAC,CAAC;EAE9BX,SAAS,CAAC,MAAM;IACd,IAAIkG,WAAW,EAAE;MACfK,cAAc,CAAC,CAAC;MAChBC,aAAa,CAAC,CAAC;MACfC,gBAAgB,CAAC,CAAC;MAClBC,qBAAqB,CAAC,CAAC;MACvBC,sBAAsB,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;EAEjB,MAAMS,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMpG,KAAK,CAACqG,GAAG,CAAC,0CAA0C,CAAC;MAC5EzD,oBAAoB,CAACwD,QAAQ,CAACE,IAAI,CAAC;IACrC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D;EACF,CAAC;;EAED;EACA/G,SAAS,CAAC,MAAM;IACd,IAAIqD,QAAQ,CAACE,WAAW,IAAIN,WAAW,EAAE;MACvC,MAAMgE,gBAAgB,GAAGpE,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAChE,QAAQ,CAACE,WAAW,CAAC,CAAC;MACrF,IAAI0D,gBAAgB,EAAE;QACpB;QACA;QACA,MAAMK,WAAW,GAAGL,gBAAgB,CAACM,UAAU,KAAKtE,WAAW,CAACsE,UAAU;;QAE1E;QACA5C,cAAc,CAAC6C,IAAI,KAAK;UACtB,GAAGA,IAAI;UACPrC,SAAS,EAAEmC,WAAW,GAAGE,IAAI,CAACrC,SAAS,GAAG,CAAC;UAC3CC,SAAS,EAAEkC,WAAW,GAAGE,IAAI,CAACpC,SAAS,GAAG,CAAC;UAC3CC,SAAS,EAAE,CAACiC,WAAW,GAAGE,IAAI,CAACnC,SAAS,GAAG;QAC7C,CAAC,CAAC,CAAC;MACL;IACF;EACF,CAAC,EAAE,CAAChC,QAAQ,CAACE,WAAW,EAAEN,WAAW,EAAEJ,SAAS,CAAC,CAAC;EAElD,MAAM4D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMpG,KAAK,CAACqG,GAAG,CAAC,+BAA+B,CAAC,CAAC,CAAC;MACnE3D,cAAc,CAAC0D,QAAQ,CAACE,IAAI,CAAC;IAC/B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAML,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI;MACF;MACA,MAAME,QAAQ,GAAG,MAAMpG,KAAK,CAACqG,GAAG,CAAC,+CAA+C,CAAC;;MAEjF;MACA,IAAIY,MAAM,GAAG,EAAE;MACf,IAAIxE,WAAW,IAAIA,WAAW,CAACyE,IAAI,EAAE;QACnC;QACAD,MAAM,GAAGxE,WAAW,CAACyE,IAAI,CACtB/D,KAAK,CAAC,GAAG,CAAC,CACVgE,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,CACzCC,IAAI,CAAC,EAAE,CAAC;MACb;;MAEA;MACA,MAAMC,KAAK,GAAG,IAAIvE,IAAI,CAAC,CAAC;MACxB,IAAIwE,aAAa;MACjB,IAAID,KAAK,CAACE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE;QAAE;QAC3BD,aAAa,GAAG,GAAGD,KAAK,CAACG,WAAW,CAAC,CAAC,IAAIH,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE;MAC5E,CAAC,MAAM;QACLF,aAAa,GAAG,GAAGD,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,CAAC,IAAIH,KAAK,CAACG,WAAW,CAAC,CAAC,GAAG,IAAI,EAAE;MAC5E;;MAEA;MACA,IAAIC,YAAY,GAAG,CAAC;MACpB,IAAIxB,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACuB,aAAa,EAAE;QAChDD,YAAY,GAAGf,QAAQ,CAACT,QAAQ,CAACE,IAAI,CAACuB,aAAa,CAAC,GAAG,CAAC;MAC1D;;MAEA;MACA,MAAMC,qBAAqB,GAAGF,YAAY,CAACG,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtE,MAAMC,aAAa,GAAG,GAAGhB,MAAM,IAAIQ,aAAa,IAAIK,qBAAqB,EAAE;;MAE3E;MACAhF,WAAW,CAACkE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPkB,cAAc,EAAED;MAClB,CAAC,CAAC,CAAC;IAEL,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC1D;EACF,CAAC;EAGD,MAAMR,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMpG,KAAK,CAACqG,GAAG,CAAC,kCAAkC,CAAC;MACpE/D,YAAY,CAAC8D,QAAQ,CAACE,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC;EAED,MAAMP,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMI,QAAQ,GAAG,MAAMpG,KAAK,CAACqG,GAAG,CAAC,iCAAiC,CAAC;MACnE7D,WAAW,CAAC4D,QAAQ,CAACE,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAGD,MAAM4B,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElB,IAAI;MAAEmB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;;IAEhC;IACA,IAAIpB,IAAI,KAAK,aAAa,EAAE;MAC1B,MAAMqB,UAAU,GAAG1B,QAAQ,CAACwB,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE;MAC5C,MAAM5B,gBAAgB,GAAGpE,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK2B,UAAU,CAAC;MAEjEzF,WAAW,CAACkE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPjE,WAAW,EAAEwF,UAAU;QACvB;QACA,IAAIvE,WAAW,IAAIyC,gBAAgB,GAAG;UACpC/C,cAAc,EAAE+C,gBAAgB,CAACS,IAAI;UACrCvD,iBAAiB,EAAE8C,gBAAgB,CAAC+B,OAAO;UAC3C5E,eAAe,EAAE6C,gBAAgB,CAACgC,KAAK;UACvC5E,oBAAoB,EAAE4C,gBAAgB,CAACM,UAAU;UACjDjD,eAAe,EAAE2C,gBAAgB,CAACiC;QACpC,CAAC,GAAG,CAAC,CAAC;MACR,CAAC,CAAC,CAAC;MACH;IACF;;IAEA;IACA,IAAIxB,IAAI,KAAK,uBAAuB,EAAE;MACpC,MAAMyB,WAAW,GAAG9B,QAAQ,CAACwB,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE;MAC7C,MAAMO,iBAAiB,GAAGvG,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK+B,WAAW,CAAC;MAEnE,IAAIC,iBAAiB,EAAE;QACrB9F,WAAW,CAACkE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPtD,cAAc,EAAEkF,iBAAiB,CAAC1B,IAAI;UACtCtD,eAAe,EAAEgF,iBAAiB,CAACH,KAAK;UACxC5E,oBAAoB,EAAE+E,iBAAiB,CAAC7B,UAAU;UAClDjD,eAAe,EAAE8E,iBAAiB,CAACF;UACnC;QACF,CAAC,CAAC,CAAC;MACL;MACA;IACF;;IAEA;IACA5F,WAAW,CAACkE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACE,IAAI,GAAGA,IAAI,KAAK,oBAAoB,GAAG2B,UAAU,CAACR,KAAK,CAAC,IAAI,CAAC,GAAGA;IACnE,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMS,iBAAiB,GAAIV,CAAC,IAAK;IAC/B,MAAMW,SAAS,GAAGX,CAAC,CAACE,MAAM,CAACU,OAAO;IAClC/E,cAAc,CAAC8E,SAAS,CAAC;IAEzB,IAAIA,SAAS,EAAE;MACb;MACA,MAAMtC,gBAAgB,GAAGpE,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAK/D,QAAQ,CAACE,WAAW,CAAC;MAE3E,IAAI0D,gBAAgB,EAAE;QACpB3D,WAAW,CAACkE,IAAI,KAAK;UACnB,GAAGA,IAAI;UACPtD,cAAc,EAAE+C,gBAAgB,CAACS,IAAI;UACrCvD,iBAAiB,EAAE8C,gBAAgB,CAAC+B,OAAO;UAC3C5E,eAAe,EAAE6C,gBAAgB,CAACgC,KAAK;UACvC5E,oBAAoB,EAAE4C,gBAAgB,CAACM,UAAU;UACjDjD,eAAe,EAAE2C,gBAAgB,CAACiC;QACpC,CAAC,CAAC,CAAC;MACL;IACF,CAAC,MAAM;MACL;MACA5F,WAAW,CAACkE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPtD,cAAc,EAAE,EAAE;QAClBC,iBAAiB,EAAE,EAAE;QACrBC,eAAe,EAAE,EAAE;QACnBC,oBAAoB,EAAE,EAAE;QACxBC,eAAe,EAAE;MACnB,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAGD,MAAMmF,gBAAgB,GAAIb,CAAC,IAAK;IAC9B,MAAM;MAAElB,IAAI;MAAEmB;IAAM,CAAC,GAAGD,CAAC,CAACE,MAAM;IAEhC,IAAIpB,IAAI,KAAK,UAAU,EAAE;MACvB,MAAMgC,WAAW,GAAGL,UAAU,CAACR,KAAK,CAAC,IAAI,CAAC;;MAE1C;MACA,IAAIa,WAAW,GAAGhF,WAAW,CAACa,kBAAkB,EAAE;QAChDY,SAAS,CAAC,QAAQzB,WAAW,CAACa,kBAAkB,wBAAwBb,WAAW,CAACG,WAAW,EAAE,CAAC;QAClG;MACF;IACF;IAEAF,cAAc,CAAC6C,IAAI,IAAI;MACrB;MACA,IAAImC,QAAQ;MACZ,IAAIjC,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,WAAW,EAAE;QAClHiC,QAAQ,GAAGN,UAAU,CAACR,KAAK,CAAC,IAAI,CAAC;MACnC,CAAC,MAAM,IAAInB,IAAI,KAAK,UAAU,EAAE;QAC9BiC,QAAQ,GAAGd,KAAK,KAAK,EAAE,GAAG,CAAC,GAAGQ,UAAU,CAACR,KAAK,CAAC,IAAI,CAAC;MACtD,CAAC,MAAM;QACLc,QAAQ,GAAGd,KAAK;MAClB;MAEA,MAAMe,OAAO,GAAG;QACd,GAAGpC,IAAI;QACP,CAACE,IAAI,GAAGiC;MACV,CAAC;;MAED;MACA,IAAIjC,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,UAAU,EAAE;QACjE;QACA,MAAMxC,QAAQ,GAAGmE,UAAU,CAACO,OAAO,CAAC1E,QAAQ,CAAC,IAAI,CAAC;QAClD,MAAM2E,cAAc,GAAGD,OAAO,CAAC3E,IAAI,IAAI,CAAC,GAAGC,QAAQ,GAAC,GAAG,CAAC;QACxD0E,OAAO,CAACpE,aAAa,GAAGoE,OAAO,CAAC7E,QAAQ,GAAG8E,cAAc;MAC3D;;MAEA;MACA,IAAInC,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,UAAU,IAC7DA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,WAAW,IAAIA,IAAI,KAAK,WAAW,EAAE;QACxE;QACAkC,OAAO,CAACnE,WAAW,GAAImE,OAAO,CAACpE,aAAa,GAAGoE,OAAO,CAACzE,SAAS,GAAI,GAAG;QACvEyE,OAAO,CAAClE,WAAW,GAAIkE,OAAO,CAACpE,aAAa,GAAGoE,OAAO,CAACxE,SAAS,GAAI,GAAG;QACvEwE,OAAO,CAACjE,WAAW,GAAIiE,OAAO,CAACpE,aAAa,GAAGoE,OAAO,CAACvE,SAAS,GAAI,GAAG;MACzE;MAEA,OAAOuE,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,mBAAmB,GAAIC,OAAO,IAAK;IACvC;IACA,MAAMC,iBAAiB,GAAG3G,QAAQ,CAACkB,KAAK,CAAC0F,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACrF,WAAW,KAAKkF,OAAO,CAAClF,WAAW,CAAC;;IAEpG;IACA,IAAIsF,yBAAyB,GAAGJ,OAAO,CAACxE,kBAAkB;IAC1D,IAAIyE,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5BG,yBAAyB,IAAI9G,QAAQ,CAACkB,KAAK,CAACyF,iBAAiB,CAAC,CAACjF,QAAQ;IACzE;;IAEA;IACA,IAAIoF,yBAAyB,IAAI,CAAC,EAAE;MAClChE,SAAS,CAAC,WAAW4D,OAAO,CAAClF,WAAW,kBAAkB,CAAC;MAC3D;IACF;;IAEA;;IAEAF,cAAc,CAAC;MACbC,KAAK,EAAEvB,QAAQ,CAACkB,KAAK,CAAC6F,MAAM,GAAG,CAAC;MAChCvF,WAAW,EAAEkF,OAAO,CAAClF,WAAW;MAChCC,QAAQ,EAAEiF,OAAO,CAACjF,QAAQ;MAC1BC,QAAQ,EAAE,CAAC;MACXC,GAAG,EAAE+E,OAAO,CAAC/E,GAAG;MAChBC,IAAI,EAAE8E,OAAO,CAAC9E,IAAI;MAClBC,QAAQ,EAAE,CAAC;MAAE;MACbI,UAAU,EAAEyE,OAAO,CAAC3C,EAAE;MACtB7B,kBAAkB,EAAE4E,yBAAyB;MAAE;MAC/C;MACAhF,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZG,aAAa,EAAEuE,OAAO,CAAC9E,IAAI;MAAE;MAC7BQ,WAAW,EAAE,CAAC;MAAE;MAChBC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;EAGJ,CAAC;;EAED;;EAGA,MAAM0E,OAAO,GAAG,MAAAA,CAAA,KAAY;IAC1B,IAAI,CAAC3F,WAAW,CAACG,WAAW,IAAI,CAACH,WAAW,CAACI,QAAQ,IAAIJ,WAAW,CAACO,IAAI,IAAI,CAAC,EAAE;MAC9EkB,SAAS,CAAC,yDAAyD,CAAC;MACpE;IACF;IAEA,IAAIzB,WAAW,CAACK,QAAQ,IAAI,CAAC,EAAE;MAC7BoB,SAAS,CAAC,oCAAoC,CAAC;MAC/C;IACF;;IAEA;IACA,IAAImE,aAAa,GAAG,KAAK;IACzB,IAAIC,SAAS,GAAG7F,WAAW,CAACY,UAAU;;IAEtC;IACA,IAAI,CAACiF,SAAS,EAAE;MACd,MAAMC,eAAe,GAAGzH,QAAQ,CAACmE,IAAI,CAACuD,CAAC,IACrCA,CAAC,CAAC5F,WAAW,CAAC6F,WAAW,CAAC,CAAC,KAAKhG,WAAW,CAACG,WAAW,CAAC6F,WAAW,CAAC,CACtE,CAAC;MAED,IAAIF,eAAe,EAAE;QACnBF,aAAa,GAAG,IAAI;QACpBC,SAAS,GAAGC,eAAe,CAACpD,EAAE;QAC9B;QACAzC,cAAc,CAAC6C,IAAI,KAAK;UACtB,GAAGA,IAAI;UACPlC,UAAU,EAAEkF,eAAe,CAACpD,EAAE;UAC9BtC,QAAQ,EAAE0F,eAAe,CAAC1F,QAAQ;UAClCE,GAAG,EAAEwF,eAAe,CAACxF,GAAG;UACxBC,IAAI,EAAEuF,eAAe,CAACvF,IAAI;UAC1BM,kBAAkB,EAAEiF,eAAe,CAACjF;QACtC,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACL;QACA,IAAI;UACF,MAAMoF,UAAU,GAAG;YACjB9F,WAAW,EAAEH,WAAW,CAACG,WAAW;YACpCC,QAAQ,EAAEJ,WAAW,CAACI,QAAQ;YAC9BS,kBAAkB,EAAE,CAAC;YAAE;YACvBP,GAAG,EAAEN,WAAW,CAACM,GAAG;YACpBC,IAAI,EAAEP,WAAW,CAACO;UACpB,CAAC;UAED,MAAM2B,QAAQ,GAAG,MAAMpG,KAAK,CAACoK,IAAI,CAAC,iCAAiC,EAAED,UAAU,CAAC;UAChFJ,SAAS,GAAG3D,QAAQ,CAACE,IAAI,CAACM,EAAE;;UAE5B;UACApE,WAAW,CAACwE,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEZ,QAAQ,CAACE,IAAI,CAAC,CAAC;;UAE7C;UACAnC,cAAc,CAAC6C,IAAI,KAAK;YACtB,GAAGA,IAAI;YACPlC,UAAU,EAAEiF;UACd,CAAC,CAAC,CAAC;;UAEH;UACAtE,kBAAkB,CAACvB,WAAW,CAACG,WAAW,CAAC;UAC3CkB,mBAAmB,CAAC,IAAI,CAAC;QAE3B,CAAC,CAAC,OAAOgB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/CZ,SAAS,CAAC,iDAAiD,CAAC;UAC5D;QACF;MACF;IACF;;IAEA;IACA,MAAM6D,iBAAiB,GAAG3G,QAAQ,CAACkB,KAAK,CAAC0F,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACrF,WAAW,KAAKH,WAAW,CAACG,WAAW,CAAC;IAExG,IAAImF,iBAAiB,KAAK,CAAC,CAAC,EAAE;MAC5B;MACA,MAAMa,YAAY,GAAG,CAAC,GAAGxH,QAAQ,CAACkB,KAAK,CAAC;MACxC,MAAMuG,YAAY,GAAGD,YAAY,CAACb,iBAAiB,CAAC;;MAEpD;MACA,IAAIM,aAAa,IAAI5F,WAAW,CAACa,kBAAkB,GAAG,CAAC,EAAE;QACvD;QACA,MAAMwF,aAAa,GAAGD,YAAY,CAAC/F,QAAQ,GAAGL,WAAW,CAACK,QAAQ;QAClE,IAAIgG,aAAa,GAAGrG,WAAW,CAACa,kBAAkB,EAAE;UAClDY,SAAS,CAAC,cAAczB,WAAW,CAACK,QAAQ,qBAAqBL,WAAW,CAACa,kBAAkB,GAAGuF,YAAY,CAAC/F,QAAQ,mBAAmB,CAAC;UAC3I;QACF;MACF;;MAEA;MACA;MACA,MAAME,IAAI,GAAGoE,UAAU,CAACyB,YAAY,CAAC7F,IAAI,CAAC,IAAI,CAAC;MAC/C,MAAMC,QAAQ,GAAGmE,UAAU,CAACyB,YAAY,CAAC5F,QAAQ,CAAC,IAAI,CAAC;MACvD,MAAM8F,gBAAgB,GAAG3B,UAAU,CAACyB,YAAY,CAAC/F,QAAQ,CAAC,IAAI,CAAC;MAC/D,MAAMkG,kBAAkB,GAAG5B,UAAU,CAAC3E,WAAW,CAACK,QAAQ,CAAC,IAAI,CAAC;MAChE,MAAM2E,WAAW,GAAGsB,gBAAgB,GAAGC,kBAAkB;;MAEzD;MACA,MAAMpB,cAAc,GAAG5E,IAAI,IAAI,CAAC,GAAGC,QAAQ,GAAC,GAAG,CAAC;MAChD,MAAMgG,eAAe,GAAGxB,WAAW,GAAGG,cAAc;;MAEpD;MACA,MAAMsB,QAAQ,GAAG9B,UAAU,CAACyB,YAAY,CAAC3F,SAAS,CAAC,IAAI,CAAC;MACxD,MAAMiG,QAAQ,GAAG/B,UAAU,CAACyB,YAAY,CAAC1F,SAAS,CAAC,IAAI,CAAC;MACxD,MAAMiG,QAAQ,GAAGhC,UAAU,CAACyB,YAAY,CAACzF,SAAS,CAAC,IAAI,CAAC;;MAExD;MACA,MAAMiG,UAAU,GAAIJ,eAAe,GAAGC,QAAQ,GAAI,GAAG;MACrD,MAAMI,UAAU,GAAIL,eAAe,GAAGE,QAAQ,GAAI,GAAG;MACrD,MAAMI,UAAU,GAAIN,eAAe,GAAGG,QAAQ,GAAI,GAAG;MAErDR,YAAY,CAACb,iBAAiB,CAAC,GAAG;QAChC,GAAGc,YAAY;QACf7F,IAAI,EAAEA,IAAI;QACVC,QAAQ,EAAEA,QAAQ;QAClBH,QAAQ,EAAE2E,WAAW;QACrBlE,aAAa,EAAE0F,eAAe;QAC9B/F,SAAS,EAAEgG,QAAQ;QACnB/F,SAAS,EAAEgG,QAAQ;QACnB/F,SAAS,EAAEgG,QAAQ;QACnB5F,WAAW,EAAE6F,UAAU;QACvB5F,WAAW,EAAE6F,UAAU;QACvB5F,WAAW,EAAE6F;MACf,CAAC;MAEDlI,WAAW,CAACkE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPjD,KAAK,EAAEsG;MACT,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACL;MACA;MACA,MAAM9F,QAAQ,GAAGsE,UAAU,CAAC3E,WAAW,CAACK,QAAQ,CAAC,IAAI,CAAC;MACtD,MAAME,IAAI,GAAGoE,UAAU,CAAC3E,WAAW,CAACO,IAAI,CAAC,IAAI,CAAC;MAC9C,MAAMC,QAAQ,GAAGmE,UAAU,CAAC3E,WAAW,CAACQ,QAAQ,CAAC,IAAI,CAAC;;MAEtD;MACA,MAAM2E,cAAc,GAAG5E,IAAI,IAAI,CAAC,GAAGC,QAAQ,GAAC,GAAG,CAAC;MAChD,MAAMuG,YAAY,GAAG1G,QAAQ,GAAG8E,cAAc;;MAE9C;MACA,MAAMsB,QAAQ,GAAG9B,UAAU,CAAC3E,WAAW,CAACS,SAAS,CAAC,IAAI,CAAC;MACvD,MAAMiG,QAAQ,GAAG/B,UAAU,CAAC3E,WAAW,CAACU,SAAS,CAAC,IAAI,CAAC;MACvD,MAAMiG,QAAQ,GAAGhC,UAAU,CAAC3E,WAAW,CAACW,SAAS,CAAC,IAAI,CAAC;;MAEvD;MACA,MAAMiG,UAAU,GAAIG,YAAY,GAAGN,QAAQ,GAAI,GAAG;MAClD,MAAMI,UAAU,GAAIE,YAAY,GAAGL,QAAQ,GAAI,GAAG;MAClD,MAAMI,UAAU,GAAIC,YAAY,GAAGJ,QAAQ,GAAI,GAAG;MAElD,MAAMzB,OAAO,GAAG;QACd,GAAGlF,WAAW;QACdK,QAAQ,EAAEA,QAAQ;QAClBE,IAAI,EAAEA,IAAI;QACVC,QAAQ,EAAEA,QAAQ;QAClBM,aAAa,EAAEiG,YAAY;QAC3BnG,UAAU,EAAEiF,SAAS;QACrBpF,SAAS,EAAEgG,QAAQ;QACnB/F,SAAS,EAAEgG,QAAQ;QACnB/F,SAAS,EAAEgG,QAAQ;QACnB5F,WAAW,EAAE6F,UAAU;QACvB5F,WAAW,EAAE6F,UAAU;QACvB5F,WAAW,EAAE6F;MACf,CAAC;MAEDlI,WAAW,CAACkE,IAAI,KAAK;QACnB,GAAGA,IAAI;QACPjD,KAAK,EAAE,CAAC,GAAGiD,IAAI,CAACjD,KAAK,EAAEqF,OAAO;MAChC,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIU,aAAa,EAAE;MACjB,MAAMoB,eAAe,GAAG3I,QAAQ,CAAC4E,GAAG,CAACoC,OAAO,IAAI;QAC9C,IAAIA,OAAO,CAAC3C,EAAE,KAAKmD,SAAS,EAAE;UAC5B,OAAO;YACL,GAAGR,OAAO;YACVxE,kBAAkB,EAAEwE,OAAO,CAACxE,kBAAkB,GAAGb,WAAW,CAACK;UAC/D,CAAC;QACH;QACA,OAAOgF,OAAO;MAChB,CAAC,CAAC;MAEF/G,WAAW,CAAC0I,eAAe,CAAC;IAC9B;;IAEA;IACA/G,cAAc,CAAC;MACbC,KAAK,EAAEvB,QAAQ,CAACkB,KAAK,CAAC6F,MAAM,GAAG,CAAC;MAChCvF,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC;MACXC,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE,CAAC;MAAE;MACbC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,UAAU,EAAE,IAAI;MAChBC,kBAAkB,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC;MAAE;MAClBC,WAAW,EAAE,CAAC;MAAE;MAChBC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMgG,UAAU,GAAIC,KAAK,IAAK;IAC5B,MAAMC,WAAW,GAAGxI,QAAQ,CAACkB,KAAK,CAACqH,KAAK,CAAC;IACzC,MAAME,QAAQ,GAAG,CAAC,GAAGzI,QAAQ,CAACkB,KAAK,CAAC;IACpCuH,QAAQ,CAACC,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC;;IAEzB;IACA,MAAMf,YAAY,GAAGiB,QAAQ,CAACnE,GAAG,CAAC,CAACuC,IAAI,EAAE8B,GAAG,MAAM;MAChD,GAAG9B,IAAI;MACPtF,KAAK,EAAEoH,GAAG,GAAG;IACf,CAAC,CAAC,CAAC;IAEH1I,WAAW,CAACkE,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjD,KAAK,EAAEsG;IACT,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMa,eAAe,GAAG3I,QAAQ,CAAC4E,GAAG,CAACoC,OAAO,IAAI;MAC9C,IAAIA,OAAO,CAAClF,WAAW,KAAKgH,WAAW,CAAChH,WAAW,EAAE;QACnD,OAAO;UACL,GAAGkF,OAAO;UACVxE,kBAAkB,EAAEwE,OAAO,CAACxE,kBAAkB,GAAGsG,WAAW,CAAC9G;QAC/D,CAAC;MACH;MACA,OAAOgF,OAAO;IAChB,CAAC,CAAC;IAEF/G,WAAW,CAAC0I,eAAe,CAAC;IAE5B/G,cAAc,CAAC6C,IAAI,KAAK;MACtB,GAAGA,IAAI;MACP5C,KAAK,EAAEiG,YAAY,CAACT,MAAM,GAAG;IAC/B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM6B,eAAe,GAAGA,CAAA,KAAM;IAC5B;IACA,MAAMC,iBAAiB,GAAG7I,QAAQ,CAACkB,KAAK,CAACoD,GAAG,CAACuC,IAAI,IAAI;MACnD;MACA,MAAMnF,QAAQ,GAAGsE,UAAU,CAACa,IAAI,CAACnF,QAAQ,CAAC,IAAI,CAAC;MAC/C,MAAME,IAAI,GAAGoE,UAAU,CAACa,IAAI,CAACjF,IAAI,CAAC,IAAI,CAAC;MACvC,MAAMC,QAAQ,GAAGmE,UAAU,CAACa,IAAI,CAAChF,QAAQ,CAAC,IAAI,CAAC;MAC/C,MAAMiG,QAAQ,GAAG9B,UAAU,CAACa,IAAI,CAAC/E,SAAS,CAAC,IAAI,CAAC;MAChD,MAAMiG,QAAQ,GAAG/B,UAAU,CAACa,IAAI,CAAC9E,SAAS,CAAC,IAAI,CAAC;MAChD,MAAMiG,QAAQ,GAAGhC,UAAU,CAACa,IAAI,CAAC7E,SAAS,CAAC,IAAI,CAAC;;MAEhD;MACA,MAAMwE,cAAc,GAAG5E,IAAI,IAAI,CAAC,GAAGC,QAAQ,GAAC,GAAG,CAAC;MAChD,MAAMuG,YAAY,GAAG1G,QAAQ,GAAG8E,cAAc;;MAE9C;MACA,MAAMyB,UAAU,GAAIG,YAAY,GAAGN,QAAQ,GAAI,GAAG;MAClD,MAAMI,UAAU,GAAIE,YAAY,GAAGL,QAAQ,GAAI,GAAG;MAClD,MAAMI,UAAU,GAAIC,YAAY,GAAGJ,QAAQ,GAAI,GAAG;MAElD,OAAO;QACL,GAAGnB,IAAI;QACP1E,aAAa,EAAEiG,YAAY;QAC3BhG,WAAW,EAAE6F,UAAU;QACvB5F,WAAW,EAAE6F,UAAU;QACvB5F,WAAW,EAAE6F;MACf,CAAC;IACH,CAAC,CAAC;;IAEF;IACA,MAAMC,YAAY,GAAGS,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAElC,IAAI,KAAKkC,GAAG,GAAGlC,IAAI,CAAC1E,aAAa,EAAE,CAAC,CAAC;IACzF,MAAM6G,cAAc,GAAGH,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAElC,IAAI,KAAKkC,GAAG,GAAGlC,IAAI,CAACzE,WAAW,EAAE,CAAC,CAAC;IACzF,MAAM6G,cAAc,GAAGJ,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAElC,IAAI,KAAKkC,GAAG,GAAGlC,IAAI,CAACxE,WAAW,EAAE,CAAC,CAAC;IACzF,MAAM6G,cAAc,GAAGL,iBAAiB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAElC,IAAI,KAAKkC,GAAG,GAAGlC,IAAI,CAACvE,WAAW,EAAE,CAAC,CAAC;;IAEzF;IACA,MAAM6G,iBAAiB,GAAGnD,UAAU,CAAChG,QAAQ,CAACU,kBAAkB,IAAI,CAAC,CAAC;;IAEtE;IACA,MAAM0I,kBAAkB,GAAGhB,YAAY,GAAGe,iBAAiB;;IAE3D;IACA;IACA,IAAIE,YAAY,GAAG,CAAC;IACpB,IAAIC,YAAY,GAAG,CAAC;IACpB,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAIJ,iBAAiB,GAAG,CAAC,IAAInJ,QAAQ,CAACE,WAAW,IAAIN,WAAW,EAAE;MAChE,MAAMgE,gBAAgB,GAAGpE,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAChE,QAAQ,CAACE,WAAW,CAAC,CAAC;MACrF,IAAI0D,gBAAgB,EAAE;QACpB,MAAMK,WAAW,GAAGL,gBAAgB,CAACM,UAAU,KAAKtE,WAAW,CAACsE,UAAU;QAE1E,IAAID,WAAW,EAAE;UACf;UACAoF,YAAY,GAAIF,iBAAiB,GAAG,CAAC,GAAI,GAAG;UAC5CG,YAAY,GAAIH,iBAAiB,GAAG,CAAC,GAAI,GAAG;QAC9C,CAAC,MAAM;UACL;UACAI,YAAY,GAAIJ,iBAAiB,GAAG,EAAE,GAAI,GAAG;QAC/C;MACF;IACF;;IAEA;IACA,MAAMK,SAAS,GAAGR,cAAc,GAAGK,YAAY;IAC/C,MAAMI,SAAS,GAAGR,cAAc,GAAGK,YAAY;IAC/C,MAAMI,SAAS,GAAGR,cAAc,GAAGK,YAAY;;IAE/C;IACA,MAAMI,UAAU,GAAGP,kBAAkB,GAAGI,SAAS,GAAGC,SAAS,GAAGC,SAAS;;IAEzE;IACA,IAAIE,aAAa,GAAG,CAAC;IACrB,IAAIC,aAAa,GAAG,CAAC;IACrB,IAAIC,aAAa,GAAG,CAAC;IAErB,IAAIV,kBAAkB,GAAG,CAAC,EAAE;MAC1BQ,aAAa,GAAIJ,SAAS,GAAGJ,kBAAkB,GAAI,GAAG;MACtDS,aAAa,GAAIJ,SAAS,GAAGL,kBAAkB,GAAI,GAAG;MACtDU,aAAa,GAAIJ,SAAS,GAAGN,kBAAkB,GAAI,GAAG;IACxD;IAEA,OAAO;MACLhB,YAAY;MACZe,iBAAiB;MACjBC,kBAAkB;MAClBW,IAAI,EAAEP,SAAS;MACfQ,IAAI,EAAEP,SAAS;MACfQ,IAAI,EAAEP,SAAS;MACfE,aAAa;MACbC,aAAa;MACbC,aAAa;MACbH,UAAU;MACV;MACAX,cAAc;MACdC,cAAc;MACdC,cAAc;MACdG,YAAY;MACZC,YAAY;MACZC;IACF,CAAC;EACH,CAAC;EAED,MAAMW,YAAY,GAAG,MAAO3E,CAAC,IAAK;IAChCA,CAAC,CAAC4E,cAAc,CAAC,CAAC;IAClB;IACA,IAAInK,QAAQ,CAACkB,KAAK,CAAC6F,MAAM,KAAK,CAAC,EAAE;MAC/BjE,SAAS,CAAC,6CAA6C,CAAC;MACxD;IACF;IACA,IAAI;MACFN,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACAmB,OAAO,CAACyG,GAAG,CAAC,0BAA0B,EAAEpK,QAAQ,CAAC;;MAEjD;MACA,MAAMqK,UAAU,GAAG;QACjB,GAAGrK,QAAQ;QACXU,kBAAkB,EAAEsF,UAAU,CAAChG,QAAQ,CAACU,kBAAkB,CAAC,IAAI,CAAC;QAChER,WAAW,EAAE8D,QAAQ,CAAChE,QAAQ,CAACE,WAAW,EAAE,EAAE,CAAC;QAC/C;QACAW,cAAc,EAAEb,QAAQ,CAACa,cAAc;QACvCC,iBAAiB,EAAEd,QAAQ,CAACc,iBAAiB;QAC7CC,eAAe,EAAEf,QAAQ,CAACe,eAAe;QACzCC,oBAAoB,EAAEhB,QAAQ,CAACgB,oBAAoB;QACnDC,eAAe,EAAEjB,QAAQ,CAACiB,eAAe;QACzCC,KAAK,EAAElB,QAAQ,CAACkB,KAAK,CAACoD,GAAG,CAACuC,IAAI,KAAK;UACjC,GAAGA,IAAI;UACPnF,QAAQ,EAAEsE,UAAU,CAACa,IAAI,CAACnF,QAAQ,CAAC;UACnCE,IAAI,EAAEoE,UAAU,CAACa,IAAI,CAACjF,IAAI;QAC5B,CAAC,CAAC;MACJ,CAAC;;MAED;MACA,IAAIyI,UAAU,CAAClK,YAAY,EAAE;QAC3B,MAAMmK,cAAc,GAAG,IAAIlK,IAAI,CAACiK,UAAU,CAAClK,YAAY,CAAC;QACxDkK,UAAU,CAAClK,YAAY,GAAGmK,cAAc,CAACjK,WAAW,CAAC,CAAC;MACxD;;MAEA;MACA,IAAI,CAACgK,UAAU,CAAC7J,OAAO,EAAE;QACvB,OAAO6J,UAAU,CAAC7J,OAAO;MAC3B,CAAC,MAAM;QACL;QACA,MAAM+J,SAAS,GAAG,IAAInK,IAAI,CAACiK,UAAU,CAAC7J,OAAO,CAAC;QAC9C6J,UAAU,CAAC7J,OAAO,GAAG+J,SAAS,CAAClK,WAAW,CAAC,CAAC;MAC9C;MAEA,MAAMkD,QAAQ,GAAG,MAAMpG,KAAK,CAACoK,IAAI,CAAC,iCAAiC,EAAE8C,UAAU,CAAC;MAChF1G,OAAO,CAACyG,GAAG,CAAC,+BAA+B,EAAE7G,QAAQ,CAACE,IAAI,CAAC;MAE3DV,WAAW,CAAC,+BAA+B,CAAC;MAC5CE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,OAAOS,KAAK,EAAE;MAAA,IAAA8G,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MACdjH,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAE,EAAA8G,eAAA,GAAA9G,KAAK,CAACH,QAAQ,cAAAiH,eAAA,uBAAdA,eAAA,CAAgB/G,IAAI,KAAIC,KAAK,CAACmH,OAAO,CAAC;;MAE/E;MACA,IAAI,EAAAJ,gBAAA,GAAA/G,KAAK,CAACH,QAAQ,cAAAkH,gBAAA,uBAAdA,gBAAA,CAAgBK,MAAM,MAAK,GAAG,KAAAJ,gBAAA,GAAIhH,KAAK,CAACH,QAAQ,cAAAmH,gBAAA,gBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjH,IAAI,cAAAkH,qBAAA,gBAAAC,sBAAA,GAApBD,qBAAA,CAAsBI,MAAM,cAAAH,sBAAA,eAA5BA,sBAAA,CAA8BI,QAAQ,CAAC,uBAAuB,CAAC,EAAE;QACrGlI,SAAS,cACPrF,OAAA;UAAAwN,QAAA,GACGvH,KAAK,CAACH,QAAQ,CAACE,IAAI,CAACsH,MAAM,eAC3BtN,OAAA;YAAAyN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN5N,OAAA;YAAG6N,IAAI,EAAC,eAAe;YAAC7F,MAAM,EAAC,QAAQ;YAAC8F,GAAG,EAAC,qBAAqB;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE,SAAS;cAAEC,cAAc,EAAE;YAAY,CAAE;YAAAT,QAAA,EAAC;UAE5H;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACP,CAAC;MACH,CAAC,MAAM;QACLvI,SAAS,CAAC,0BAA0B,CAAC;MACvC;IACF,CAAC,SAAS;MACRN,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMmJ,MAAM,GAAG/C,eAAe,CAAC,CAAC;;EAEhC;EACA;EACAjM,SAAS,CAAC,MAAM;IACd,IAAIqD,QAAQ,CAACE,WAAW,IAAIN,WAAW,EAAE;MACvC,MAAMgM,QAAQ,GAAGpM,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAChE,QAAQ,CAACE,WAAW,CAAC,CAAC;MAC7EyD,OAAO,CAACyG,GAAG,CAAC,sBAAsB,EAAEwB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE1H,UAAU,CAAC;MACzDP,OAAO,CAACyG,GAAG,CAAC,qBAAqB,EAAExK,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsE,UAAU,CAAC;MAC3DP,OAAO,CAACyG,GAAG,CAAC,iBAAiB,EAAE,CAAAwB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE1H,UAAU,OAAKtE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsE,UAAU,EAAC;IAClF;EACF,CAAC,EAAE,CAAClE,QAAQ,CAACE,WAAW,EAAEN,WAAW,EAAEJ,SAAS,CAAC,CAAC;;EAElD;EACA7C,SAAS,CAAC,MAAM;IACd,IAAIqD,QAAQ,CAACkB,KAAK,CAAC6F,MAAM,GAAG,CAAC,EAAE;MAC7BpD,OAAO,CAACyG,GAAG,CAAC,yBAAyB,CAAC;MACtCpK,QAAQ,CAACkB,KAAK,CAAC2K,OAAO,CAAC,CAAChF,IAAI,EAAE0B,KAAK,KAAK;QACtC5E,OAAO,CAACyG,GAAG,CAAC,QAAQ7B,KAAK,GAAG,CAAC,KAAK1B,IAAI,CAACrF,WAAW,EAAE,CAAC;QACrDmC,OAAO,CAACyG,GAAG,CAAC,oBAAoBvD,IAAI,CAAC1E,aAAa,EAAE,CAAC;QACrDwB,OAAO,CAACyG,GAAG,CAAC,gBAAgBvD,IAAI,CAAC/E,SAAS,cAAc+E,IAAI,CAACzE,WAAW,EAAE,CAAC;QAC3EuB,OAAO,CAACyG,GAAG,CAAC,gBAAgBvD,IAAI,CAAC9E,SAAS,cAAc8E,IAAI,CAACxE,WAAW,EAAE,CAAC;QAC3EsB,OAAO,CAACyG,GAAG,CAAC,gBAAgBvD,IAAI,CAAC7E,SAAS,cAAc6E,IAAI,CAACvE,WAAW,EAAE,CAAC;MAC7E,CAAC,CAAC;MACFqB,OAAO,CAACyG,GAAG,CAAC,oBAAoB,CAAC;MACjCzG,OAAO,CAACyG,GAAG,CAAC,iBAAiBuB,MAAM,CAAC5B,IAAI,EAAE,CAAC;MAC3CpG,OAAO,CAACyG,GAAG,CAAC,iBAAiBuB,MAAM,CAAC3B,IAAI,EAAE,CAAC;MAC3CrG,OAAO,CAACyG,GAAG,CAAC,iBAAiBuB,MAAM,CAAC1B,IAAI,EAAE,CAAC;MAC3CtG,OAAO,CAACyG,GAAG,CAAC,kBAAkBuB,MAAM,CAAChC,UAAU,EAAE,CAAC;IACpD;EACF,CAAC,EAAE,CAAC3J,QAAQ,CAACkB,KAAK,EAAEyK,MAAM,CAAC,CAAC;EAG5B,oBACElO,OAAA;IAAKqO,SAAS,EAAC,oBAAoB;IAAAb,QAAA,gBACjCxN,OAAA;MAAKqO,SAAS,EAAC,iBAAiB;MAAAb,QAAA,eAC9BxN,OAAA;QAAAwN,QAAA,gBACExN,OAAA;UAAIqO,SAAS,EAAC,gBAAgB;UAAAb,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtD5N,OAAA;UAAGqO,SAAS,EAAC,mBAAmB;UAAAb,QAAA,EAAC;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLvL,iBAAiB,IAAI,CAACA,iBAAiB,CAACiM,iBAAiB,iBACxDtO,OAAA,CAACuO,KAAK;MAACC,OAAO,EAAC,SAAS;MAACH,SAAS,EAAC,MAAM;MAAAb,QAAA,gBACvCxN,OAAA,CAACuO,KAAK,CAACE,OAAO;QAAAjB,QAAA,gBACZxN,OAAA;UAAGqO,SAAS,EAAC;QAAkC;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,yBAEtD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAChB5N,OAAA;QAAGqO,SAAS,EAAC,MAAM;QAAAb,QAAA,EAAC;MAEpB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJ5N,OAAA,CAACV,GAAG;QAAAkO,QAAA,gBACFxN,OAAA,CAACT,GAAG;UAACmP,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACTxN,OAAA;YAAAwN,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB5N,OAAA;YAAIqO,SAAS,EAAC,MAAM;YAAAb,QAAA,gBAClBxN,OAAA;cAAIqO,SAAS,EAAE,CAAAhO,qBAAA,GAAAgC,iBAAiB,CAACsM,oBAAoB,cAAAtO,qBAAA,gBAAAC,sBAAA,GAAtCD,qBAAA,CAAwCuO,aAAa,cAAAtO,sBAAA,eAArDA,sBAAA,CAAuDuO,YAAY,GAAG,cAAc,GAAG,aAAc;cAAArB,QAAA,gBAClHxN,OAAA;gBAAGqO,SAAS,EAAE,OAAO,CAAA9N,sBAAA,GAAA8B,iBAAiB,CAACsM,oBAAoB,cAAApO,sBAAA,gBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCqO,aAAa,cAAApO,sBAAA,eAArDA,sBAAA,CAAuDqO,YAAY,GAAG,UAAU,GAAG,UAAU;cAAQ;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,gBAEjI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5N,OAAA;cAAIqO,SAAS,EAAE,CAAA5N,sBAAA,GAAA4B,iBAAiB,CAACsM,oBAAoB,cAAAlO,sBAAA,gBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCmO,aAAa,cAAAlO,sBAAA,eAArDA,sBAAA,CAAuDoO,aAAa,GAAG,cAAc,GAAG,aAAc;cAAAtB,QAAA,gBACnHxN,OAAA;gBAAGqO,SAAS,EAAE,OAAO,CAAA1N,sBAAA,GAAA0B,iBAAiB,CAACsM,oBAAoB,cAAAhO,sBAAA,gBAAAC,sBAAA,GAAtCD,sBAAA,CAAwCiO,aAAa,cAAAhO,sBAAA,eAArDA,sBAAA,CAAuDkO,aAAa,GAAG,UAAU,GAAG,UAAU;cAAQ;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,iBAElI;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5N,OAAA;cAAIqO,SAAS,EAAE,CAAAxN,sBAAA,GAAAwB,iBAAiB,CAACsM,oBAAoB,cAAA9N,sBAAA,gBAAAC,sBAAA,GAAtCD,sBAAA,CAAwC+N,aAAa,cAAA9N,sBAAA,eAArDA,sBAAA,CAAuDiO,KAAK,GAAG,cAAc,GAAG,aAAc;cAAAvB,QAAA,gBAC3GxN,OAAA;gBAAGqO,SAAS,EAAE,OAAO,CAAAtN,sBAAA,GAAAsB,iBAAiB,CAACsM,oBAAoB,cAAA5N,sBAAA,gBAAAC,uBAAA,GAAtCD,sBAAA,CAAwC6N,aAAa,cAAA5N,uBAAA,eAArDA,uBAAA,CAAuD+N,KAAK,GAAG,UAAU,GAAG,UAAU;cAAQ;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAE1H;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN5N,OAAA,CAACT,GAAG;UAACmP,EAAE,EAAE,CAAE;UAAAlB,QAAA,gBACTxN,OAAA;YAAAwN,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB5N,OAAA;YAAIqO,SAAS,EAAC,MAAM;YAAAb,QAAA,gBAClBxN,OAAA;cAAIqO,SAAS,EAAE,CAAApN,uBAAA,GAAAoB,iBAAiB,CAACsM,oBAAoB,cAAA1N,uBAAA,gBAAAC,uBAAA,GAAtCD,uBAAA,CAAwC+N,eAAe,cAAA9N,uBAAA,eAAvDA,uBAAA,CAAyDgH,OAAO,GAAG,cAAc,GAAG,aAAc;cAAAsF,QAAA,gBAC/GxN,OAAA;gBAAGqO,SAAS,EAAE,OAAO,CAAAlN,uBAAA,GAAAkB,iBAAiB,CAACsM,oBAAoB,cAAAxN,uBAAA,gBAAAC,uBAAA,GAAtCD,uBAAA,CAAwC6N,eAAe,cAAA5N,uBAAA,eAAvDA,uBAAA,CAAyD8G,OAAO,GAAG,UAAU,GAAG,UAAU;cAAQ;gBAAAuF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,WAE9H;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5N,OAAA;cAAIqO,SAAS,EAAE,CAAAhN,uBAAA,GAAAgB,iBAAiB,CAACsM,oBAAoB,cAAAtN,uBAAA,gBAAAC,uBAAA,GAAtCD,uBAAA,CAAwC2N,eAAe,cAAA1N,uBAAA,eAAvDA,uBAAA,CAAyD6G,KAAK,GAAG,cAAc,GAAG,aAAc;cAAAqF,QAAA,gBAC7GxN,OAAA;gBAAGqO,SAAS,EAAE,OAAO,CAAA9M,uBAAA,GAAAc,iBAAiB,CAACsM,oBAAoB,cAAApN,uBAAA,gBAAAC,uBAAA,GAAtCD,uBAAA,CAAwCyN,eAAe,cAAAxN,uBAAA,eAAvDA,uBAAA,CAAyD2G,KAAK,GAAG,UAAU,GAAG,UAAU;cAAQ;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAE5H;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5N,OAAA;cAAIqO,SAAS,EAAE,CAAA5M,uBAAA,GAAAY,iBAAiB,CAACsM,oBAAoB,cAAAlN,uBAAA,gBAAAC,uBAAA,GAAtCD,uBAAA,CAAwCuN,eAAe,cAAAtN,uBAAA,eAAvDA,uBAAA,CAAyD0G,KAAK,GAAG,cAAc,GAAG,aAAc;cAAAoF,QAAA,gBAC7GxN,OAAA;gBAAGqO,SAAS,EAAE,OAAO,CAAA1M,uBAAA,GAAAU,iBAAiB,CAACsM,oBAAoB,cAAAhN,uBAAA,gBAAAC,uBAAA,GAAtCD,uBAAA,CAAwCqN,eAAe,cAAApN,uBAAA,eAAvDA,uBAAA,CAAyDwG,KAAK,GAAG,UAAU,GAAG,UAAU;cAAQ;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,SAE5H;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN5N,OAAA;QAAAyN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN5N,OAAA;QAAKqO,SAAS,EAAC,mDAAmD;QAAAb,QAAA,gBAChExN,OAAA;UAAAwN,QAAA,GAAM,sBAAoB,eAAAxN,OAAA;YAAAwN,QAAA,GAASnL,iBAAiB,CAAC4M,6BAA6B,EAAC,GAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpG5N,OAAA,CAACZ,MAAM;UACLoP,OAAO,EAAC,SAAS;UACjBU,IAAI,EAAC,IAAI;UACTC,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACxB,IAAI,GAAG,4BAA6B;UAAAL,QAAA,gBAEnExN,OAAA;YAAGqO,SAAS,EAAC;UAAuB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,oBAE3C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eAED5N,OAAA,CAACb,IAAI;MAACmQ,QAAQ,EAAE7C,YAAa;MAAAe,QAAA,gBAC3BxN,OAAA,CAACX,IAAI;QAACgP,SAAS,EAAC,mBAAmB;QAAAb,QAAA,gBACjCxN,OAAA,CAACX,IAAI,CAACkQ,MAAM;UAAA/B,QAAA,eACVxN,OAAA;YAAAwN,QAAA,gBAAIxN,OAAA;cAAGqO,SAAS,EAAC;YAAiB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,qBAAiB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACd5N,OAAA,CAACX,IAAI,CAACmQ,IAAI;UAAAhC,QAAA,gBACRxN,OAAA,CAACV,GAAG;YAAC+O,SAAS,EAAC,MAAM;YAAAb,QAAA,gBACnBxN,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAACrB,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpE5N,OAAA,CAACF,UAAU;kBACT6P,QAAQ,EAAEpN,QAAQ,CAACG,YAAY,GAAG,IAAIC,IAAI,CAACJ,QAAQ,CAACG,YAAY,CAAC,GAAG,IAAIC,IAAI,CAAC,CAAE;kBAC/EiN,QAAQ,EAAGC,IAAI,IAAKrN,WAAW,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAEhE,YAAY,EAAEmN,IAAI,GAAGA,IAAI,CAACjN,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;kBAAE,CAAC,CAAC,CAAE;kBACvJwL,SAAS,EAAC,mCAAmC;kBAC7CyB,UAAU,EAAC,YAAY;kBACvBC,QAAQ,EAAC,aAAa;kBACtBC,eAAe,EAAC,cAAc;kBAC9BC,QAAQ;gBAAA;kBAAAxC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAACrB,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjE5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXvJ,IAAI,EAAC,WAAW;kBAChBmB,KAAK,EAAExF,QAAQ,CAACO,SAAU;kBAC1B8M,QAAQ,EAAE/H,YAAa;kBACvBwG,SAAS,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAACrB,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/D5N,OAAA,CAACF,UAAU;kBACT6P,QAAQ,EAAEpN,QAAQ,CAACQ,OAAO,GAAG,IAAIJ,IAAI,CAACJ,QAAQ,CAACQ,OAAO,CAAC,GAAG,IAAK;kBAC/D6M,QAAQ,EAAGC,IAAI,IAAKrN,WAAW,CAACkE,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE3D,OAAO,EAAE8M,IAAI,GAAGA,IAAI,CAACjN,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG;kBAAG,CAAC,CAAC,CAAE;kBAC9GwL,SAAS,EAAC,mCAAmC;kBAC7CyB,UAAU,EAAC,YAAY;kBACvBC,QAAQ,EAAC,aAAa;kBACtBC,eAAe,EAAC,cAAc;kBAC9BI,WAAW;gBAAA;kBAAA3C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN5N,OAAA,CAACX,IAAI;YAACgP,SAAS,EAAC,mBAAmB;YAAAb,QAAA,gBACjCxN,OAAA,CAACX,IAAI,CAACkQ,MAAM;cAAA/B,QAAA,eACVxN,OAAA;gBAAAwN,QAAA,gBAAIxN,OAAA;kBAAGqO,SAAS,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,oBAAgB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACd5N,OAAA,CAACX,IAAI,CAACmQ,IAAI;cAAAhC,QAAA,gBACVxN,OAAA,CAACV,GAAG;gBAAAkO,QAAA,eACFxN,OAAA,CAACT,GAAG;kBAACmP,EAAE,EAAE,EAAG;kBAAAlB,QAAA,eACVxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;oBAACpB,SAAS,EAAC,MAAM;oBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;sBAACrB,SAAS,EAAC,oBAAoB;sBAAAb,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvE5N,OAAA,CAACb,IAAI,CAACkR,MAAM;sBACVzJ,IAAI,EAAC,aAAa;sBAClBqJ,QAAQ;sBACRlI,KAAK,EAAExF,QAAQ,CAACE,WAAY;sBAC5BmN,QAAQ,EAAE/H,YAAa;sBACvBwG,SAAS,EAAC,qBAAqB;sBAAAb,QAAA,gBAE/BxN,OAAA;wBAAQ+H,KAAK,EAAC,EAAE;wBAAAyF,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACxC7L,SAAS,CAAC8E,GAAG,CAACsH,QAAQ,iBACrBnO,OAAA;wBAA0B+H,KAAK,EAAEoG,QAAQ,CAAC7H,EAAG;wBAAAkH,QAAA,EAC1CW,QAAQ,CAACvH;sBAAI,GADHuH,QAAQ,CAAC7H,EAAE;wBAAAmH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEhB,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAELrL,QAAQ,CAACE,WAAW,iBACnBzC,OAAA;gBAAKqO,SAAS,EAAC,4DAA4D;gBAAAb,QAAA,EACxE,CAAC,MAAM;kBACN,MAAMW,QAAQ,GAAGpM,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAChE,QAAQ,CAACE,WAAW,CAAC,CAAC;kBAC7E,OAAO0L,QAAQ,gBACbnO,OAAA,CAAAE,SAAA;oBAAAsN,QAAA,gBACExN,OAAA;sBAAGqO,SAAS,EAAC,MAAM;sBAAAb,QAAA,gBAACxN,OAAA;wBAAAwN,QAAA,EAAQ;sBAAK;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACO,QAAQ,CAACvH,IAAI;oBAAA;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC9D5N,OAAA;sBAAGqO,SAAS,EAAC,MAAM;sBAAAb,QAAA,gBAACxN,OAAA;wBAAAwN,QAAA,EAAQ;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACO,QAAQ,CAACjG,OAAO;oBAAA;sBAAAuF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACpE5N,OAAA;sBAAGqO,SAAS,EAAC,MAAM;sBAAAb,QAAA,gBAACxN,OAAA;wBAAAwN,QAAA,EAAQ;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACO,QAAQ,CAAChG,KAAK,EAAC,IAAE,EAACgG,QAAQ,CAAC1H,UAAU,EAAC,GAAC;oBAAA;sBAAAgH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACxF5N,OAAA;sBAAGqO,SAAS,EAAC,MAAM;sBAAAb,QAAA,gBAACxN,OAAA;wBAAAwN,QAAA,EAAQ;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACO,QAAQ,CAAC/F,KAAK;oBAAA;sBAAAqF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChE5N,OAAA;sBAAGqO,SAAS,EAAC,MAAM;sBAAAb,QAAA,gBAACxN,OAAA;wBAAAwN,QAAA,EAAQ;sBAAO;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,KAAC,EAACO,QAAQ,CAACW,aAAa;oBAAA;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,eACzE,CAAC,GACD,IAAI;gBACV,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAGP5N,OAAA,CAACX,IAAI;YAACgP,SAAS,EAAC,mBAAmB;YAAAb,QAAA,gBACjCxN,OAAA,CAACX,IAAI,CAACkQ,MAAM;cAAA/B,QAAA,eACVxN,OAAA;gBAAAwN,QAAA,gBAAIxN,OAAA;kBAAGqO,SAAS,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,wBAAoB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACd5N,OAAA,CAACX,IAAI,CAACmQ,IAAI;cAAAhC,QAAA,gBAEVxN,OAAA,CAACb,IAAI,CAACmR,KAAK;gBACTH,IAAI,EAAC,UAAU;gBACf7J,EAAE,EAAC,eAAe;gBAClBiK,KAAK,EAAC,eAAe;gBACrB7H,OAAO,EAAEhF,WAAY;gBACrBkM,QAAQ,EAAEpH,iBAAkB;gBAC5B6F,SAAS,EAAC;cAAM;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjB,CAAC,EAED,CAAClK,WAAW,iBACX1D,OAAA,CAACV,GAAG;gBAAAkO,QAAA,eACFxN,OAAA,CAACT,GAAG;kBAACmP,EAAE,EAAE,EAAG;kBAAAlB,QAAA,eACVxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;oBAACpB,SAAS,EAAC,MAAM;oBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;sBAACrB,SAAS,EAAC,oBAAoB;sBAAAb,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACvE5N,OAAA,CAACb,IAAI,CAACkR,MAAM;sBACVzJ,IAAI,EAAC,uBAAuB;sBAC5BgJ,QAAQ,EAAE/H,YAAa;sBACvBwG,SAAS,EAAC,qBAAqB;sBAAAb,QAAA,gBAE/BxN,OAAA;wBAAQ+H,KAAK,EAAC,EAAE;wBAAAyF,QAAA,EAAC;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,EACxC7L,SAAS,CAAC8E,GAAG,CAACsH,QAAQ,iBACrBnO,OAAA;wBAA0B+H,KAAK,EAAEoG,QAAQ,CAAC7H,EAAG;wBAAAkH,QAAA,EAC1CW,QAAQ,CAACvH;sBAAI,GADHuH,QAAQ,CAAC7H,EAAE;wBAAAmH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAEhB,CACT,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS,CAAC,eACd5N,OAAA,CAACb,IAAI,CAACqR,IAAI;sBAACnC,SAAS,EAAC,YAAY;sBAAAb,QAAA,EAAC;oBAElC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,eAED5N,OAAA,CAACV,GAAG;gBAAAkO,QAAA,gBACFxN,OAAA,CAACT,GAAG;kBAACmP,EAAE,EAAE,CAAE;kBAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;oBAACpB,SAAS,EAAC,MAAM;oBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;sBAACrB,SAAS,EAAC,oBAAoB;sBAAAb,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC5D5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXvJ,IAAI,EAAC,gBAAgB;sBACrBmB,KAAK,EAAExF,QAAQ,CAACa,cAAe;sBAC/BwM,QAAQ,EAAE/H,YAAa;sBACvBwG,SAAS,EAAC,sBAAsB;sBAChCoC,WAAW,EAAC,sBAAsB;sBAClCR,QAAQ,EAAE,CAACvM;oBAAY;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;kBAACmP,EAAE,EAAE,CAAE;kBAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;oBAACpB,SAAS,EAAC,MAAM;oBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;sBAACrB,SAAS,EAAC,oBAAoB;sBAAAb,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7D5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXvJ,IAAI,EAAC,iBAAiB;sBACtBmB,KAAK,EAAExF,QAAQ,CAACiB,eAAgB;sBAChCoM,QAAQ,EAAE/H,YAAa;sBACvBwG,SAAS,EAAC,sBAAsB;sBAChCoC,WAAW,EAAC,aAAa;sBACzBR,QAAQ,EAAE,CAACvM;oBAAY;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5N,OAAA,CAACV,GAAG;gBAAAkO,QAAA,eACFxN,OAAA,CAACT,GAAG;kBAACmP,EAAE,EAAE,EAAG;kBAAAlB,QAAA,eACVxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;oBAACpB,SAAS,EAAC,MAAM;oBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;sBAACrB,SAAS,EAAC,oBAAoB;sBAAAb,QAAA,EAAC;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/D5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;sBACXQ,EAAE,EAAC,UAAU;sBACbC,IAAI,EAAE,CAAE;sBACR/J,IAAI,EAAC,mBAAmB;sBACxBmB,KAAK,EAAExF,QAAQ,CAACc,iBAAkB;sBAClCuM,QAAQ,EAAE/H,YAAa;sBACvBwG,SAAS,EAAC,sBAAsB;sBAChCoC,WAAW,EAAC,wBAAwB;sBACpCR,QAAQ,EAAE,CAACvM;oBAAY;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACF5N,OAAA,CAACb,IAAI,CAACqR,IAAI;sBAACnC,SAAS,EAAC,YAAY;sBAAAb,QAAA,EAAC;oBAElC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAW,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN5N,OAAA,CAACV,GAAG;gBAAAkO,QAAA,gBACFxN,OAAA,CAACT,GAAG;kBAACmP,EAAE,EAAE,CAAE;kBAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;oBAACpB,SAAS,EAAC,MAAM;oBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;sBAACrB,SAAS,EAAC,oBAAoB;sBAAAb,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC7D5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXvJ,IAAI,EAAC,iBAAiB;sBACtBmB,KAAK,EAAExF,QAAQ,CAACe,eAAgB;sBAChCsM,QAAQ,EAAE/H,YAAa;sBACvBwG,SAAS,EAAC,sBAAsB;sBAChCoC,WAAW,EAAC,aAAa;sBACzBR,QAAQ,EAAE,CAACvM;oBAAY;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;kBAACmP,EAAE,EAAE,CAAE;kBAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;oBAACpB,SAAS,EAAC,MAAM;oBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;sBAACrB,SAAS,EAAC,oBAAoB;sBAAAb,QAAA,EAAC;oBAAU;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAClE5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;sBACXC,IAAI,EAAC,MAAM;sBACXvJ,IAAI,EAAC,sBAAsB;sBAC3BmB,KAAK,EAAExF,QAAQ,CAACgB,oBAAqB;sBACrCqM,QAAQ,EAAE/H,YAAa;sBACvBwG,SAAS,EAAC,sBAAsB;sBAChCoC,WAAW,EAAC,kBAAkB;sBAC9BR,QAAQ,EAAE,CAACvM;oBAAY;sBAAA+J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEP5N,OAAA,CAACV,GAAG;YAAAkO,QAAA,gBACFxN,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAACrB,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtE5N,OAAA,CAACb,IAAI,CAACkR,MAAM;kBACVzJ,IAAI,EAAC,gBAAgB;kBACrBmB,KAAK,EAAExF,QAAQ,CAACS,cAAe;kBAC/B4M,QAAQ,EAAE/H,YAAa;kBACvBwG,SAAS,EAAC,qBAAqB;kBAAAb,QAAA,gBAE/BxN,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAyF,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B5N,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAyF,QAAA,EAAC;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAACrB,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5E5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbvJ,IAAI,EAAC,oBAAoB;kBACzBgK,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,MAAM;kBACX9I,KAAK,EAAExF,QAAQ,CAACU,kBAAmB;kBACnC2M,QAAQ,EAAE/H,YAAa;kBACvBwG,SAAS,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5N,OAAA,CAACV,GAAG;YAAAkO,QAAA,gBACFxN,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAACrB,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACtE5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXvJ,IAAI,EAAC,gBAAgB;kBACrBmB,KAAK,EAAExF,QAAQ,CAACW,cAAe;kBAC/B0M,QAAQ,EAAE/H,YAAa;kBACvBwG,SAAS,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAACrB,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACxE5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXvJ,IAAI,EAAC,kBAAkB;kBACvBmB,KAAK,EAAExF,QAAQ,CAACY,gBAAiB;kBACjCyM,QAAQ,EAAE/H,YAAa;kBACvBwG,SAAS,EAAC;gBAAsB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP5N,OAAA,CAACX,IAAI;QAACgP,SAAS,EAAC,mBAAmB;QAAAb,QAAA,gBACjCxN,OAAA,CAACX,IAAI,CAACkQ,MAAM;UAAA/B,QAAA,eACVxN,OAAA;YAAAwN,QAAA,gBAAIxN,OAAA;cAAGqO,SAAS,EAAC;YAAsB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,UAAM;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACd5N,OAAA,CAACX,IAAI,CAACmQ,IAAI;UAAAhC,QAAA,gBAERxN,OAAA,CAACV,GAAG;YAAAkO,QAAA,eACFxN,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,EAAG;cAAAlB,QAAA,eACVxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACvC5N,OAAA,CAACb,IAAI,CAACkR,MAAM;kBACVT,QAAQ,EAAG9H,CAAC,IAAK;oBACf,MAAMmB,OAAO,GAAGhH,QAAQ,CAACmE,IAAI,CAACuD,CAAC,IAAIA,CAAC,CAACrD,EAAE,KAAKC,QAAQ,CAACuB,CAAC,CAACE,MAAM,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC;oBACzE,IAAIkB,OAAO,EAAED,mBAAmB,CAACC,OAAO,CAAC;kBAC3C,CAAE;kBAAAuE,QAAA,gBAEFxN,OAAA;oBAAQ+H,KAAK,EAAC,EAAE;oBAAAyF,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvC3L,QAAQ,CAAC4E,GAAG,CAACoC,OAAO,iBACnBjJ,OAAA;oBAEE+H,KAAK,EAAEkB,OAAO,CAAC3C,EAAG;oBAClBwK,QAAQ,EAAE7H,OAAO,CAACxE,kBAAkB,IAAI,CAAE;oBAAA+I,QAAA,GAEzCvE,OAAO,CAAClF,WAAW,EAAC,WAAI,EAACkF,OAAO,CAAC9E,IAAI,CAAC4M,OAAO,CAAC,CAAC,CAAC,EAAC,KAAG,EAAC9H,OAAO,CAACxE,kBAAkB,EAAC,YACjF,EAACwE,OAAO,CAACxE,kBAAkB,IAAI,CAAC,GAAG,iBAAiB,GAAG,EAAE;kBAAA,GALpDwE,OAAO,CAAC3C,EAAE;oBAAAmH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAMT,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN5N,OAAA,CAACV,GAAG;YAAC+O,SAAS,EAAC,iBAAiB;YAAAb,QAAA,gBAC9BxN,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpC5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXvJ,IAAI,EAAC,aAAa;kBAClBmB,KAAK,EAAEnE,WAAW,CAACG,WAAY;kBAC/B6L,QAAQ,EAAEjH;gBAAiB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAAAiO,QAAA,eACFxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAChC5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXvJ,IAAI,EAAC,UAAU;kBACfmB,KAAK,EAAEnE,WAAW,CAACI,QAAS;kBAC5B4L,QAAQ,EAAEjH;gBAAiB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAAAiO,QAAA,eACFxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5B5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbvJ,IAAI,EAAC,UAAU;kBACfgK,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,GAAG;kBACR9I,KAAK,EAAEnE,WAAW,CAACK,QAAS;kBAC5B2L,QAAQ,EAAEjH;gBAAiB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAAAiO,QAAA,eACFxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC5B5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXvJ,IAAI,EAAC,KAAK;kBACVmB,KAAK,EAAEnE,WAAW,CAACM,GAAI;kBACvB0L,QAAQ,EAAEjH;gBAAiB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAAAiO,QAAA,eACFxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7B5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbvJ,IAAI,EAAC,MAAM;kBACXgK,GAAG,EAAC,GAAG;kBACPC,IAAI,EAAC,MAAM;kBACX9I,KAAK,EAAEnE,WAAW,CAACO,IAAK;kBACxByL,QAAQ,EAAEjH;gBAAiB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAAAiO,QAAA,eACFxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACrC5N,OAAA,CAACb,IAAI,CAAC+Q,OAAO;kBACXC,IAAI,EAAC,QAAQ;kBACbvJ,IAAI,EAAC,UAAU;kBACfgK,GAAG,EAAC,GAAG;kBACPI,GAAG,EAAC,KAAK;kBACTH,IAAI,EAAC,GAAG;kBACR9I,KAAK,EAAEnE,WAAW,CAACQ,QAAS;kBAC5BwL,QAAQ,EAAEjH;gBAAiB;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAAAiO,QAAA,eACFxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAClC5N,OAAA,CAACb,IAAI,CAACkR,MAAM;kBACVzJ,IAAI,EAAC,gBAAgB;kBACrBmB,KAAK,EAAE,GAAGnE,WAAW,CAACS,SAAS,IAAIT,WAAW,CAACU,SAAS,EAAG;kBAC3DsL,QAAQ,EAAG9H,CAAC,IAAK;oBACf,MAAM,CAACwE,IAAI,EAAEC,IAAI,CAAC,GAAGzE,CAAC,CAACE,MAAM,CAACD,KAAK,CAAClF,KAAK,CAAC,GAAG,CAAC,CAACgE,GAAG,CAAC0B,UAAU,CAAC;oBAC9D1E,cAAc,CAAC6C,IAAI,KAAK;sBACtB,GAAGA,IAAI;sBACPrC,SAAS,EAAEiI,IAAI,IAAI,CAAC;sBACpBhI,SAAS,EAAEiI,IAAI,IAAI,CAAC;sBACpBhI,SAAS,EAAE,CAAC,CAAC;oBACf,CAAC,CAAC,CAAC;kBACL,CAAE;kBACFuM,QAAQ,EACN,CAACvO,QAAQ,CAACE,WAAW,IACrB,CAACN,WAAW,IACXI,QAAQ,CAACE,WAAW,IAAI,EAAAZ,eAAA,GAAAE,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAChE,QAAQ,CAACE,WAAW,CAAC,CAAC,cAAAZ,eAAA,uBAA5DA,eAAA,CAA8D4E,UAAU,OAAKtE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsE,UAAU,CAC9H;kBAAA+G,QAAA,gBAEDxN,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAyF,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACrC5N,OAAA;oBAAQ+H,KAAK,EAAC,aAAa;oBAAAyF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5N,OAAA;oBAAQ+H,KAAK,EAAC,aAAa;oBAAAyF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5N,OAAA;oBAAQ+H,KAAK,EAAC,aAAa;oBAAAyF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5N,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC5N,OAAA;oBAAQ+H,KAAK,EAAC,aAAa;oBAAAyF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5N,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC5N,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC5N,OAAA;oBAAQ+H,KAAK,EAAC,OAAO;oBAAAyF,QAAA,EAAC;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC5N,OAAA;oBAAQ+H,KAAK,EAAC,aAAa;oBAAAyF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5N,OAAA;oBAAQ+H,KAAK,EAAC,aAAa;oBAAAyF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5N,OAAA;oBAAQ+H,KAAK,EAAC,aAAa;oBAAAyF,QAAA,EAAC;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD5N,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClC5N,OAAA;oBAAQ+H,KAAK,EAAC,KAAK;oBAAAyF,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAAAiO,QAAA,eACFxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC7B5N,OAAA,CAACb,IAAI,CAACkR,MAAM;kBACVzJ,IAAI,EAAC,WAAW;kBAChBmB,KAAK,EAAEnE,WAAW,CAACW,SAAU;kBAC7BqL,QAAQ,EAAG9H,CAAC,IAAK;oBACf,MAAMyC,QAAQ,GAAGhC,UAAU,CAACT,CAAC,CAACE,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC;oBAChDlE,cAAc,CAAC6C,IAAI,KAAK;sBACtB,GAAGA,IAAI;sBACPnC,SAAS,EAAEgG,QAAQ;sBACnBlG,SAAS,EAAE,CAAC;sBAAE;sBACdC,SAAS,EAAE,CAAC,CAAE;oBAChB,CAAC,CAAC,CAAC;kBACL,CAAE;kBACFwM,QAAQ,EACN,CAACvO,QAAQ,CAACE,WAAW,IACrB,CAACN,WAAW,IACXI,QAAQ,CAACE,WAAW,IAAI,EAAAX,gBAAA,GAAAC,SAAS,CAACqE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAAChE,QAAQ,CAACE,WAAW,CAAC,CAAC,cAAAX,gBAAA,uBAA5DA,gBAAA,CAA8D2E,UAAU,OAAKtE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsE,UAAU,CAC9H;kBAAA+G,QAAA,gBAEDxN,OAAA;oBAAQ+H,KAAK,EAAC,GAAG;oBAAAyF,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnC5N,OAAA;oBAAQ+H,KAAK,EAAC,GAAG;oBAAAyF,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5B5N,OAAA;oBAAQ+H,KAAK,EAAC,OAAO;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC5N,OAAA;oBAAQ+H,KAAK,EAAC,OAAO;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC5N,OAAA;oBAAQ+H,KAAK,EAAC,GAAG;oBAAAyF,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5B5N,OAAA;oBAAQ+H,KAAK,EAAC,GAAG;oBAAAyF,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5B5N,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAyF,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B5N,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAyF,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B5N,OAAA;oBAAQ+H,KAAK,EAAC,IAAI;oBAAAyF,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9B5N,OAAA;oBAAQ+H,KAAK,EAAC,GAAG;oBAAAyF,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5B5N,OAAA;oBAAQ+H,KAAK,EAAC,OAAO;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC5N,OAAA;oBAAQ+H,KAAK,EAAC,OAAO;oBAAAyF,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACpC5N,OAAA;oBAAQ+H,KAAK,EAAC,GAAG;oBAAAyF,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC5B5N,OAAA;oBAAQ+H,KAAK,EAAC,GAAG;oBAAAyF,QAAA,EAAC;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eACN5N,OAAA,CAACT,GAAG;cAACmP,EAAE,EAAE,CAAE;cAAAlB,QAAA,eACTxN,OAAA,CAACb,IAAI,CAACsQ,KAAK;gBAACpB,SAAS,EAAC,MAAM;gBAAAb,QAAA,gBAC1BxN,OAAA,CAACb,IAAI,CAACuQ,KAAK;kBAAAlC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eAC/B5N,OAAA;kBAAAwN,QAAA,eACExN,OAAA,CAACZ,MAAM;oBACLiP,SAAS,EAAC,sCAAsC;oBAChDc,OAAO,EAAE5F,OAAQ;oBACjB0H,KAAK,EAAC,UAAU;oBAAAzD,QAAA,eAEhBxN,OAAA;sBAAGqO,SAAS,EAAC;oBAAa;sBAAAZ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELrL,QAAQ,CAACkB,KAAK,CAAC6F,MAAM,GAAG,CAAC,iBACxBtJ,OAAA;YAAKqO,SAAS,EAAC,kBAAkB;YAAAb,QAAA,eACjCxN,OAAA,CAACR,KAAK;cAAC6O,SAAS,EAAC,kBAAkB;cAAAb,QAAA,gBACjCxN,OAAA;gBAAAwN,QAAA,eACExN,OAAA;kBAAAwN,QAAA,gBACExN,OAAA;oBAAAwN,QAAA,EAAI;kBAAO;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChB5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpB5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrB5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACZ5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACZ5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjB5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAS;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClB5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACb5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACf5N,OAAA;oBAAAwN,QAAA,EAAI;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACR5N,OAAA;gBAAAwN,QAAA,EACGjL,QAAQ,CAACkB,KAAK,CAACoD,GAAG,CAAC,CAACuC,IAAI,EAAE0B,KAAK,kBAC9B9K,OAAA;kBAAAwN,QAAA,gBACExN,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAACtF;kBAAK;oBAAA2J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACrB5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAACrF;kBAAW;oBAAA0J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC3B5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAACpF;kBAAQ;oBAAAyJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxB5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAACnF;kBAAQ;oBAAAwJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxB5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAAClF;kBAAG;oBAAAuJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACnB5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAACjF,IAAI,CAAC4M,OAAO,CAAC,CAAC;kBAAC;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/B5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAAChF,QAAQ,GAAG,CAAC,GAAG,GAAGgF,IAAI,CAAChF,QAAQ,GAAG,GAAG;kBAAG;oBAAAqJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxD5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAAC/E,SAAS,GAAG,CAAC,GAAG,GAAG+E,IAAI,CAAC/E,SAAS,OAAO+E,IAAI,CAAC9E,SAAS,GAAG,GAAG;kBAAG;oBAAAmJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC/E5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAAC7E,SAAS,GAAG,CAAC,GAAG,GAAG6E,IAAI,CAAC7E,SAAS,GAAG,GAAG;kBAAG;oBAAAkJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1D5N,OAAA;oBAAAwN,QAAA,EAAKpE,IAAI,CAAC1E,aAAa,CAACqM,OAAO,CAAC,CAAC;kBAAC;oBAAAtD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxC5N,OAAA;oBAAAwN,QAAA,eACExN,OAAA,CAACZ,MAAM;sBACLiP,SAAS,EAAC,+CAA+C;sBACzDc,OAAO,EAAEA,CAAA,KAAMtE,UAAU,CAACC,KAAK,CAAE;sBAAA0C,QAAA,gBAEjCxN,OAAA;wBAAGqO,SAAS,EAAC;sBAAmB;wBAAAZ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,WACvC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC;gBAAA,GAlBE9C,KAAK;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBV,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP5N,OAAA,CAACX,IAAI;QAACgP,SAAS,EAAC,mBAAmB;QAAAb,QAAA,gBACjCxN,OAAA,CAACX,IAAI,CAACkQ,MAAM;UAAA/B,QAAA,eACVxN,OAAA;YAAAwN,QAAA,gBAAIxN,OAAA;cAAGqO,SAAS,EAAC;YAAmB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mBAAe;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACd5N,OAAA,CAACX,IAAI,CAACmQ,IAAI;UAAAhC,QAAA,eAERxN,OAAA;YAAKqO,SAAS,EAAC,gBAAgB;YAAAb,QAAA,gBAC7BxN,OAAA;cAAKqO,SAAS,EAAC,oBAAoB;cAAAb,QAAA,gBACjCxN,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/D5N,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,QAAC,EAACU,MAAM,CAACvD,YAAY,CAACoG,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E,CAAC,eACN5N,OAAA;cAAKqO,SAAS,EAAC,oBAAoB;cAAAb,QAAA,gBACjCxN,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,EAAC;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrE5N,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,QAAC,EAACU,MAAM,CAACxC,iBAAiB,CAACqF,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E,CAAC,eACN5N,OAAA;cAAKqO,SAAS,EAAC,oBAAoB;cAAAb,QAAA,gBACjCxN,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChE5N,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,QAAC,EAACU,MAAM,CAACvC,kBAAkB,CAACoF,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChF,CAAC,EAGLM,MAAM,CAAC5B,IAAI,GAAG,CAAC,iBACdtM,OAAA;cAAKqO,SAAS,EAAC,oBAAoB;cAAAb,QAAA,gBACjCxN,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,WAEpC,EAACU,MAAM,CAAC3C,cAAc,GAAG,CAAC,IAAI2C,MAAM,CAACtC,YAAY,GAAG,CAAC,iBACnD5L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,gBAC3B,EAACU,MAAM,CAAC3C,cAAc,CAACwF,OAAO,CAAC,CAAC,CAAC,EAAC,eAAQ,EAAC7C,MAAM,CAACtC,YAAY,CAACmF,OAAO,CAAC,CAAC,CAAC,EAAC,GACrF;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR,EACAM,MAAM,CAAC3C,cAAc,GAAG,CAAC,IAAI2C,MAAM,CAACtC,YAAY,KAAK,CAAC,iBACrD5L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,gBAC3B,EAACU,MAAM,CAAC3C,cAAc,CAACwF,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR,EACAM,MAAM,CAAC3C,cAAc,KAAK,CAAC,IAAI2C,MAAM,CAACtC,YAAY,GAAG,CAAC,iBACrD5L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,cAC7B,EAACU,MAAM,CAACtC,YAAY,CAACmF,OAAO,CAAC,CAAC,CAAC,EAAC,GACzC;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN5N,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,QAAC,EAACU,MAAM,CAAC5B,IAAI,CAACyE,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CACN,EAGAM,MAAM,CAAC3B,IAAI,GAAG,CAAC,iBACdvM,OAAA;cAAKqO,SAAS,EAAC,oBAAoB;cAAAb,QAAA,gBACjCxN,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,WAEpC,EAACU,MAAM,CAAC1C,cAAc,GAAG,CAAC,IAAI0C,MAAM,CAACrC,YAAY,GAAG,CAAC,iBACnD7L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,gBAC3B,EAACU,MAAM,CAAC1C,cAAc,CAACuF,OAAO,CAAC,CAAC,CAAC,EAAC,eAAQ,EAAC7C,MAAM,CAACrC,YAAY,CAACkF,OAAO,CAAC,CAAC,CAAC,EAAC,GACrF;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR,EACAM,MAAM,CAAC1C,cAAc,GAAG,CAAC,IAAI0C,MAAM,CAACrC,YAAY,KAAK,CAAC,iBACrD7L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,gBAC3B,EAACU,MAAM,CAAC1C,cAAc,CAACuF,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR,EACAM,MAAM,CAAC1C,cAAc,KAAK,CAAC,IAAI0C,MAAM,CAACrC,YAAY,GAAG,CAAC,iBACrD7L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,cAC7B,EAACU,MAAM,CAACrC,YAAY,CAACkF,OAAO,CAAC,CAAC,CAAC,EAAC,GACzC;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN5N,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,QAAC,EAACU,MAAM,CAAC3B,IAAI,CAACwE,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CACN,EAGAM,MAAM,CAAC1B,IAAI,GAAG,CAAC,iBACdxM,OAAA;cAAKqO,SAAS,EAAC,oBAAoB;cAAAb,QAAA,gBACjCxN,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,WAEpC,EAACU,MAAM,CAACzC,cAAc,GAAG,CAAC,IAAIyC,MAAM,CAACpC,YAAY,GAAG,CAAC,iBACnD9L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,gBAC3B,EAACU,MAAM,CAACzC,cAAc,CAACsF,OAAO,CAAC,CAAC,CAAC,EAAC,eAAQ,EAAC7C,MAAM,CAACpC,YAAY,CAACiF,OAAO,CAAC,CAAC,CAAC,EAAC,GACrF;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR,EACAM,MAAM,CAACzC,cAAc,GAAG,CAAC,IAAIyC,MAAM,CAACpC,YAAY,KAAK,CAAC,iBACrD9L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,gBAC3B,EAACU,MAAM,CAACzC,cAAc,CAACsF,OAAO,CAAC,CAAC,CAAC,EAAC,GAC7C;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR,EACAM,MAAM,CAACzC,cAAc,KAAK,CAAC,IAAIyC,MAAM,CAACpC,YAAY,GAAG,CAAC,iBACrD9L,OAAA;kBAAOqO,SAAS,EAAC,oBAAoB;kBAAAb,QAAA,GAAC,cAC7B,EAACU,MAAM,CAACpC,YAAY,CAACiF,OAAO,CAAC,CAAC,CAAC,EAAC,GACzC;gBAAA;kBAAAtD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CACR;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACN5N,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,QAAC,EAACU,MAAM,CAAC1B,IAAI,CAACuE,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CACN,eAED5N,OAAA;cAAKqO,SAAS,EAAC,qCAAqC;cAAAb,QAAA,gBAClDxN,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/D5N,OAAA;gBAAKqO,SAAS,EAAC,sBAAsB;gBAAAb,QAAA,GAAC,QAAC,EAACU,MAAM,CAAChC,UAAU,CAAC6E,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAtD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEP5N,OAAA;QAAKqO,SAAS,EAAC,gCAAgC;QAACN,KAAK,EAAE;UAAEmD,YAAY,EAAE;QAAQ,CAAE;QAAA1D,QAAA,gBAC/ExN,OAAA,CAACZ,MAAM;UACLiP,SAAS,EAAC,aAAa;UACvBc,OAAO,EAAEA,CAAA,KAAM3J,QAAQ,CAAC,WAAW,CAAE;UACrCsL,QAAQ,EAAEhM,OAAQ;UAAA0I,QAAA,gBAElBxN,OAAA;YAAGqO,SAAS,EAAC;UAAmB;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,WACvC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT5N,OAAA,CAACZ,MAAM;UACLiP,SAAS,EAAC,iCAAiC;UAC3C8B,IAAI,EAAC,QAAQ;UACbW,QAAQ,EAAEhM,OAAO,IAAIvC,QAAQ,CAACkB,KAAK,CAAC6F,MAAM,KAAK,CAAE;UAAAkE,QAAA,EAEhD1I,OAAO,gBACN9E,OAAA,CAAAE,SAAA;YAAAsN,QAAA,gBACExN,OAAA;cAAMqO,SAAS,EAAC,uCAAuC;cAAC8C,IAAI,EAAC,QAAQ;cAAC,eAAY;YAAM;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,uBAElG;UAAA,eAAE,CAAC,gBAEH5N,OAAA,CAAAE,SAAA;YAAAsN,QAAA,gBAAExN,OAAA;cAAGqO,SAAS,EAAC;YAAkB;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,mBAAe;UAAA,eAAE;QACxD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP5N,OAAA,CAACP,KAAK;MACJ2R,IAAI,EAAEpM,gBAAiB;MACvBqM,MAAM,EAAEA,CAAA,KAAMpM,mBAAmB,CAAC,KAAK,CAAE;MACzCqM,QAAQ;MACRjD,SAAS,EAAC,uBAAuB;MAAAb,QAAA,gBAEjCxN,OAAA,CAACP,KAAK,CAAC8P,MAAM;QAACgC,WAAW;QAAClD,SAAS,EAAC,uBAAuB;QAAAb,QAAA,eACzDxN,OAAA,CAACP,KAAK,CAAC+R,KAAK;UAAAhE,QAAA,gBAACxN,OAAA;YAAGqO,SAAS,EAAC;UAA0B;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,8BAA0B;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF,CAAC,eACf5N,OAAA,CAACP,KAAK,CAAC+P,IAAI;QAACnB,SAAS,EAAC,MAAM;QAAAb,QAAA,eAC1BxN,OAAA;UAAKqO,SAAS,EAAC,kBAAkB;UAAAb,QAAA,gBAC/BxN,OAAA;YAAKqO,SAAS,EAAC,6BAA6B;YAAAb,QAAA,eAC1CxN,OAAA;cAAGqO,SAAS,EAAC;YAAoC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACN5N,OAAA;YAAIqO,SAAS,EAAC,MAAM;YAAAb,QAAA,GAAC,YAAS,EAACtI,eAAe,EAAC,gFAA6E;UAAA;YAAAuI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjI5N,OAAA;YAAGqO,SAAS,EAAC,YAAY;YAAAb,QAAA,EAAC;UAA0D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACb5N,OAAA,CAACP,KAAK,CAACgS,MAAM;QAACpD,SAAS,EAAC,iCAAiC;QAAAb,QAAA,eACvDxN,OAAA,CAACZ,MAAM;UACLoP,OAAO,EAAC,SAAS;UACjBU,IAAI,EAAC,IAAI;UACTb,SAAS,EAAC,MAAM;UAChBc,OAAO,EAAEA,CAAA,KAAMlK,mBAAmB,CAAC,KAAK,CAAE;UAAAuI,QAAA,EAC3C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV;AAACxN,EAAA,CA1gDuBD,aAAa;EAAA,QAgDXR,OAAO,EACiBC,QAAQ,EACvCC,WAAW;AAAA;AAAA6R,EAAA,GAlDNvR,aAAa;AAAA,IAAAuR,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}