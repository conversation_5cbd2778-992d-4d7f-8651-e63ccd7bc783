{"ast": null, "code": "var _jsxFileName = \"D:\\\\invoice_project\\\\gst-billing-software\\\\frontend\\\\src\\\\pages\\\\auth\\\\Register.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, But<PERSON>, Card, Container, Row, Col } from 'react-bootstrap';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../context/AuthContext';\nimport { useToast } from '../../context/ToastContext';\nimport ProductBranding from '../../components/common/ProductBranding';\nimport '../../styles/auth.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nexport default function Register() {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    confirmPassword: '',\n    company_name: '',\n    mobile_number: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const {\n    register\n  } = useAuth();\n  const {\n    showError,\n    showSuccess\n  } = useToast();\n  const navigate = useNavigate();\n  const handleChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData({\n      ...formData,\n      [name]: value\n    });\n  };\n  const validateEmail = email => {\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\n    return emailRegex.test(email);\n  };\n  const validateMobileNumber = mobile => {\n    return /^\\d{10}$/.test(mobile);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validation\n    if (!validateEmail(formData.email)) {\n      return setError('Please enter a valid email address');\n    }\n    if (!validateMobileNumber(formData.mobile_number)) {\n      return setError('Mobile number must be exactly 10 digits');\n    }\n    if (formData.password.length < 6) {\n      return setError('Password should be at least 6 characters');\n    }\n    if (formData.password !== formData.confirmPassword) {\n      return setError('Passwords do not match');\n    }\n    if (!formData.company_name.trim()) {\n      return setError('Company name is required');\n    }\n    try {\n      setError('');\n      setLoading(true);\n\n      // Send the registration data directly\n      await register(formData);\n      navigate('/dashboard');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError('Failed to create account: ' + (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.detail) || err.message));\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        className: \"justify-content-center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          xs: 12,\n          lg: 10,\n          xl: 9,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"auth-card registration-card\",\n            children: [/*#__PURE__*/_jsxDEV(Card.Header, {\n              className: \"auth-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"auth-logo\",\n                children: /*#__PURE__*/_jsxDEV(ProductBranding, {\n                  variant: \"centered\",\n                  logoSize: \"large\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"auth-title\",\n                children: \"Create Account\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"auth-subtitle\",\n                children: \"Join our platform to streamline your business\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Card.Body, {\n              className: \"auth-body\",\n              children: [error && /*#__PURE__*/_jsxDEV(Alert, {\n                variant: \"danger\",\n                className: \"mb-4\",\n                dismissible: true,\n                children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                  className: \"fas fa-exclamation-circle me-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 21\n                }, this), error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Form, {\n                onSubmit: handleSubmit,\n                className: \"registration-form\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"registration-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"section-title\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-user-circle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 105,\n                      columnNumber: 51\n                    }, this), \"Account Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                    className: \"mb-3\",\n                    children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                      className: \"auth-form-label\",\n                      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                        className: \"fas fa-envelope\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 108,\n                        columnNumber: 25\n                      }, this), \"Email Address\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                      type: \"email\",\n                      name: \"email\",\n                      required: true,\n                      value: formData.email,\n                      onChange: handleChange,\n                      className: \"auth-input\",\n                      placeholder: \"Enter your email address\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 110,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                      className: \"text-muted\",\n                      children: \"This will be used as your username\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-lock\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 126,\n                            columnNumber: 29\n                          }, this), \"Password\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 125,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"password\",\n                          name: \"password\",\n                          required: true,\n                          value: formData.password,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Create a password\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 128,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                          className: \"text-muted\",\n                          children: \"Must be at least 6 characters\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 137,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 124,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 123,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-check-circle\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 143,\n                            columnNumber: 29\n                          }, this), \"Confirm Password\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 142,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"password\",\n                          name: \"confirmPassword\",\n                          required: true,\n                          value: formData.confirmPassword,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Confirm your password\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 145,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 141,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 140,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"registration-section\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                    className: \"section-title\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-building me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 160,\n                      columnNumber: 51\n                    }, this), \"Basic Information\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Row, {\n                    children: [/*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-briefcase\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 165,\n                            columnNumber: 29\n                          }, this), \"Company Name\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 164,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"text\",\n                          name: \"company_name\",\n                          required: true,\n                          value: formData.company_name,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Enter your company name\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 167,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 163,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Col, {\n                      md: 6,\n                      children: /*#__PURE__*/_jsxDEV(Form.Group, {\n                        className: \"mb-3\",\n                        children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                          className: \"auth-form-label\",\n                          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                            className: \"fas fa-mobile-alt\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 181,\n                            columnNumber: 29\n                          }, this), \"Mobile Number\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 180,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                          type: \"tel\",\n                          name: \"mobile_number\",\n                          required: true,\n                          value: formData.mobile_number,\n                          onChange: handleChange,\n                          className: \"auth-input\",\n                          placeholder: \"Enter 10-digit mobile number\",\n                          maxLength: \"10\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 183,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 179,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"alert alert-info\",\n                    children: [/*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-info-circle me-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 198,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Complete your profile after registration\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 199,\n                      columnNumber: 80\n                    }, this), \"You'll need to add company details (address, state, GSTIN) and bank information before creating invoices.\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 197,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 159,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"terms-section mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(Form.Check, {\n                    type: \"checkbox\",\n                    id: \"terms-checkbox\",\n                    label: /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: [\"I agree to the \", /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"#\",\n                        className: \"auth-link\",\n                        children: \"Terms of Service\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 51\n                      }, this), \" and \", /*#__PURE__*/_jsxDEV(Link, {\n                        to: \"#\",\n                        className: \"auth-link\",\n                        children: \"Privacy Policy\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 208,\n                        columnNumber: 114\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 30\n                    }, this),\n                    className: \"auth-checkbox\",\n                    required: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 205,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  disabled: loading,\n                  className: \"auth-btn w-100 mt-3\",\n                  type: \"submit\",\n                  children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"spinner-border spinner-border-sm me-2\",\n                      role: \"status\",\n                      \"aria-hidden\": \"true\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 25\n                    }, this), \"Creating Account...\"]\n                  }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                    children: [\"Create Account \", /*#__PURE__*/_jsxDEV(\"i\", {\n                      className: \"fas fa-arrow-right ms-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 40\n                    }, this)]\n                  }, void 0, true)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"auth-footer\",\n                children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/login\",\n                  className: \"auth-link\",\n                  children: \"Sign In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 44\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n}\n_s(Register, \"1ILANatP0OlBcm5mhxbX9jmFDNY=\", false, function () {\n  return [useAuth, useToast, useNavigate];\n});\n_c = Register;\nvar _c;\n$RefreshReg$(_c, \"Register\");", "map": {"version": 3, "names": ["React", "useState", "Form", "<PERSON><PERSON>", "Card", "Container", "Row", "Col", "Link", "useNavigate", "useAuth", "useToast", "ProductBranding", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Register", "_s", "formData", "setFormData", "email", "password", "confirmPassword", "company_name", "mobile_number", "loading", "setLoading", "register", "showError", "showSuccess", "navigate", "handleChange", "e", "name", "value", "target", "validateEmail", "emailRegex", "test", "validateMobileNumber", "mobile", "handleSubmit", "preventDefault", "setError", "length", "trim", "err", "_err$response", "_err$response$data", "response", "data", "detail", "message", "className", "children", "xs", "lg", "xl", "Header", "variant", "logoSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Body", "error", "<PERSON><PERSON>", "dismissible", "onSubmit", "Group", "Label", "Control", "type", "required", "onChange", "placeholder", "md", "max<PERSON><PERSON><PERSON>", "Check", "id", "label", "to", "disabled", "role", "_c", "$RefreshReg$"], "sources": ["D:/invoice_project/gst-billing-software/frontend/src/pages/auth/Register.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { <PERSON>, <PERSON><PERSON>, Card, Container, Row, Col } from 'react-bootstrap';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { useAuth } from '../../context/AuthContext';\r\nimport { useToast } from '../../context/ToastContext';\r\nimport ProductBranding from '../../components/common/ProductBranding';\r\nimport '../../styles/auth.css';\r\n\r\n\r\n\r\nexport default function Register() {\r\n  const [formData, setFormData] = useState({\r\n    email: '',\r\n    password: '',\r\n    confirmPassword: '',\r\n    company_name: '',\r\n    mobile_number: ''\r\n  });\r\n  const [loading, setLoading] = useState(false);\r\n  const { register } = useAuth();\r\n  const { showError, showSuccess } = useToast();\r\n  const navigate = useNavigate();\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData({\r\n      ...formData,\r\n      [name]: value\r\n    });\r\n  };\r\n\r\n  const validateEmail = (email) => {\r\n    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n    return emailRegex.test(email);\r\n  };\r\n\r\n  const validateMobileNumber = (mobile) => {\r\n    return /^\\d{10}$/.test(mobile);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    // Validation\r\n    if (!validateEmail(formData.email)) {\r\n      return setError('Please enter a valid email address');\r\n    }\r\n\r\n    if (!validateMobileNumber(formData.mobile_number)) {\r\n      return setError('Mobile number must be exactly 10 digits');\r\n    }\r\n\r\n    if (formData.password.length < 6) {\r\n      return setError('Password should be at least 6 characters');\r\n    }\r\n\r\n    if (formData.password !== formData.confirmPassword) {\r\n      return setError('Passwords do not match');\r\n    }\r\n\r\n    if (!formData.company_name.trim()) {\r\n      return setError('Company name is required');\r\n    }\r\n\r\n    try {\r\n      setError('');\r\n      setLoading(true);\r\n\r\n      // Send the registration data directly\r\n      await register(formData);\r\n      navigate('/dashboard');\r\n    } catch (err) {\r\n      setError('Failed to create account: ' + (err.response?.data?.detail || err.message));\r\n    }\r\n    setLoading(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <Container>\r\n        <Row className=\"justify-content-center\">\r\n          <Col xs={12} lg={10} xl={9}>\r\n            <Card className=\"auth-card registration-card\">\r\n              <Card.Header className=\"auth-header\">\r\n                <div className=\"auth-logo\">\r\n                  <ProductBranding\r\n                    variant=\"centered\"\r\n                    logoSize=\"large\"\r\n                  />\r\n                </div>\r\n                <h2 className=\"auth-title\">Create Account</h2>\r\n                <p className=\"auth-subtitle\">Join our platform to streamline your business</p>\r\n              </Card.Header>\r\n\r\n              <Card.Body className=\"auth-body\">\r\n                {error && (\r\n                  <Alert variant=\"danger\" className=\"mb-4\" dismissible>\r\n                    <i className=\"fas fa-exclamation-circle me-2\"></i>\r\n                    {error}\r\n                  </Alert>\r\n                )}\r\n\r\n                <Form onSubmit={handleSubmit} className=\"registration-form\">\r\n                  <div className=\"registration-section\">\r\n                    <h5 className=\"section-title\"><i className=\"fas fa-user-circle me-2\"></i>Account Information</h5>\r\n                    <Form.Group className=\"mb-3\">\r\n                      <Form.Label className=\"auth-form-label\">\r\n                        <i className=\"fas fa-envelope\"></i>Email Address\r\n                      </Form.Label>\r\n                      <Form.Control\r\n                        type=\"email\"\r\n                        name=\"email\"\r\n                        required\r\n                        value={formData.email}\r\n                        onChange={handleChange}\r\n                        className=\"auth-input\"\r\n                        placeholder=\"Enter your email address\"\r\n                      />\r\n                      <small className=\"text-muted\">This will be used as your username</small>\r\n                    </Form.Group>\r\n\r\n                    <Row>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-lock\"></i>Password\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"password\"\r\n                            name=\"password\"\r\n                            required\r\n                            value={formData.password}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Create a password\"\r\n                          />\r\n                          <small className=\"text-muted\">Must be at least 6 characters</small>\r\n                        </Form.Group>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-check-circle\"></i>Confirm Password\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"password\"\r\n                            name=\"confirmPassword\"\r\n                            required\r\n                            value={formData.confirmPassword}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Confirm your password\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n                  </div>\r\n\r\n                  <div className=\"registration-section\">\r\n                    <h5 className=\"section-title\"><i className=\"fas fa-building me-2\"></i>Basic Information</h5>\r\n                    <Row>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-briefcase\"></i>Company Name\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"text\"\r\n                            name=\"company_name\"\r\n                            required\r\n                            value={formData.company_name}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Enter your company name\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                      <Col md={6}>\r\n                        <Form.Group className=\"mb-3\">\r\n                          <Form.Label className=\"auth-form-label\">\r\n                            <i className=\"fas fa-mobile-alt\"></i>Mobile Number\r\n                          </Form.Label>\r\n                          <Form.Control\r\n                            type=\"tel\"\r\n                            name=\"mobile_number\"\r\n                            required\r\n                            value={formData.mobile_number}\r\n                            onChange={handleChange}\r\n                            className=\"auth-input\"\r\n                            placeholder=\"Enter 10-digit mobile number\"\r\n                            maxLength=\"10\"\r\n                          />\r\n                        </Form.Group>\r\n                      </Col>\r\n                    </Row>\r\n\r\n                    <div className=\"alert alert-info\">\r\n                      <i className=\"fas fa-info-circle me-2\"></i>\r\n                      <strong>Complete your profile after registration</strong><br />\r\n                      You'll need to add company details (address, state, GSTIN) and bank information before creating invoices.\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"terms-section mb-4\">\r\n                    <Form.Check\r\n                      type=\"checkbox\"\r\n                      id=\"terms-checkbox\"\r\n                      label={<span>I agree to the <Link to=\"#\" className=\"auth-link\">Terms of Service</Link> and <Link to=\"#\" className=\"auth-link\">Privacy Policy</Link></span>}\r\n                      className=\"auth-checkbox\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <Button\r\n                    disabled={loading}\r\n                    className=\"auth-btn w-100 mt-3\"\r\n                    type=\"submit\"\r\n                  >\r\n                    {loading ? (\r\n                      <>\r\n                        <span className=\"spinner-border spinner-border-sm me-2\" role=\"status\" aria-hidden=\"true\"></span>\r\n                        Creating Account...\r\n                      </>\r\n                    ) : (\r\n                      <>Create Account <i className=\"fas fa-arrow-right ms-2\"></i></>\r\n                    )}\r\n                  </Button>\r\n                </Form>\r\n\r\n                <div className=\"auth-footer\">\r\n                  Already have an account? <Link to=\"/login\" className=\"auth-link\">Sign In</Link>\r\n                </div>\r\n              </Card.Body>\r\n            </Card>\r\n          </Col>\r\n        </Row>\r\n      </Container>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,SAAS,EAAEC,GAAG,EAAEC,GAAG,QAAQ,iBAAiB;AACzE,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,2BAA2B;AACnD,SAASC,QAAQ,QAAQ,4BAA4B;AACrD,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAO,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAI/B,eAAe,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EACjC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC;IACvCoB,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,YAAY,EAAE,EAAE;IAChBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAE2B;EAAS,CAAC,GAAGlB,OAAO,CAAC,CAAC;EAC9B,MAAM;IAAEmB,SAAS;IAAEC;EAAY,CAAC,GAAGnB,QAAQ,CAAC,CAAC;EAC7C,MAAMoB,QAAQ,GAAGtB,WAAW,CAAC,CAAC;EAE9B,MAAMuB,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChChB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACe,IAAI,GAAGC;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAME,aAAa,GAAIhB,KAAK,IAAK;IAC/B,MAAMiB,UAAU,GAAG,kDAAkD;IACrE,OAAOA,UAAU,CAACC,IAAI,CAAClB,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMmB,oBAAoB,GAAIC,MAAM,IAAK;IACvC,OAAO,UAAU,CAACF,IAAI,CAACE,MAAM,CAAC;EAChC,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOT,CAAC,IAAK;IAChCA,CAAC,CAACU,cAAc,CAAC,CAAC;;IAElB;IACA,IAAI,CAACN,aAAa,CAAClB,QAAQ,CAACE,KAAK,CAAC,EAAE;MAClC,OAAOuB,QAAQ,CAAC,oCAAoC,CAAC;IACvD;IAEA,IAAI,CAACJ,oBAAoB,CAACrB,QAAQ,CAACM,aAAa,CAAC,EAAE;MACjD,OAAOmB,QAAQ,CAAC,yCAAyC,CAAC;IAC5D;IAEA,IAAIzB,QAAQ,CAACG,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;MAChC,OAAOD,QAAQ,CAAC,0CAA0C,CAAC;IAC7D;IAEA,IAAIzB,QAAQ,CAACG,QAAQ,KAAKH,QAAQ,CAACI,eAAe,EAAE;MAClD,OAAOqB,QAAQ,CAAC,wBAAwB,CAAC;IAC3C;IAEA,IAAI,CAACzB,QAAQ,CAACK,YAAY,CAACsB,IAAI,CAAC,CAAC,EAAE;MACjC,OAAOF,QAAQ,CAAC,0BAA0B,CAAC;IAC7C;IAEA,IAAI;MACFA,QAAQ,CAAC,EAAE,CAAC;MACZjB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMC,QAAQ,CAACT,QAAQ,CAAC;MACxBY,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOgB,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZL,QAAQ,CAAC,4BAA4B,IAAI,EAAAI,aAAA,GAAAD,GAAG,CAACG,QAAQ,cAAAF,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcG,IAAI,cAAAF,kBAAA,uBAAlBA,kBAAA,CAAoBG,MAAM,KAAIL,GAAG,CAACM,OAAO,CAAC,CAAC;IACtF;IACA1B,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACEb,OAAA;IAAKwC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BzC,OAAA,CAACT,SAAS;MAAAkD,QAAA,eACRzC,OAAA,CAACR,GAAG;QAACgD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrCzC,OAAA,CAACP,GAAG;UAACiD,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAH,QAAA,eACzBzC,OAAA,CAACV,IAAI;YAACkD,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC3CzC,OAAA,CAACV,IAAI,CAACuD,MAAM;cAACL,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAClCzC,OAAA;gBAAKwC,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxBzC,OAAA,CAACF,eAAe;kBACdgD,OAAO,EAAC,UAAU;kBAClBC,QAAQ,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACNnD,OAAA;gBAAIwC,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAc;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CnD,OAAA;gBAAGwC,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAA6C;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eAEdnD,OAAA,CAACV,IAAI,CAAC8D,IAAI;cAACZ,SAAS,EAAC,WAAW;cAAAC,QAAA,GAC7BY,KAAK,iBACJrD,OAAA,CAACsD,KAAK;gBAACR,OAAO,EAAC,QAAQ;gBAACN,SAAS,EAAC,MAAM;gBAACe,WAAW;gBAAAd,QAAA,gBAClDzC,OAAA;kBAAGwC,SAAS,EAAC;gBAAgC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACjDE,KAAK;cAAA;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACR,eAEDnD,OAAA,CAACZ,IAAI;gBAACoE,QAAQ,EAAE5B,YAAa;gBAACY,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBACzDzC,OAAA;kBAAKwC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCzC,OAAA;oBAAIwC,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAACzC,OAAA;sBAAGwC,SAAS,EAAC;oBAAyB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,uBAAmB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACjGnD,OAAA,CAACZ,IAAI,CAACqE,KAAK;oBAACjB,SAAS,EAAC,MAAM;oBAAAC,QAAA,gBAC1BzC,OAAA,CAACZ,IAAI,CAACsE,KAAK;sBAAClB,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBACrCzC,OAAA;wBAAGwC,SAAS,EAAC;sBAAiB;wBAAAQ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,iBACrC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,IAAI,CAACuE,OAAO;sBACXC,IAAI,EAAC,OAAO;sBACZxC,IAAI,EAAC,OAAO;sBACZyC,QAAQ;sBACRxC,KAAK,EAAEhB,QAAQ,CAACE,KAAM;sBACtBuD,QAAQ,EAAE5C,YAAa;sBACvBsB,SAAS,EAAC,YAAY;sBACtBuB,WAAW,EAAC;oBAA0B;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvC,CAAC,eACFnD,OAAA;sBAAOwC,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAkC;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9D,CAAC,eAEbnD,OAAA,CAACR,GAAG;oBAAAiD,QAAA,gBACFzC,OAAA,CAACP,GAAG;sBAACuE,EAAE,EAAE,CAAE;sBAAAvB,QAAA,eACTzC,OAAA,CAACZ,IAAI,CAACqE,KAAK;wBAACjB,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BzC,OAAA,CAACZ,IAAI,CAACsE,KAAK;0BAAClB,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCzC,OAAA;4BAAGwC,SAAS,EAAC;0BAAa;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,YACjC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,IAAI,CAACuE,OAAO;0BACXC,IAAI,EAAC,UAAU;0BACfxC,IAAI,EAAC,UAAU;0BACfyC,QAAQ;0BACRxC,KAAK,EAAEhB,QAAQ,CAACG,QAAS;0BACzBsD,QAAQ,EAAE5C,YAAa;0BACvBsB,SAAS,EAAC,YAAY;0BACtBuB,WAAW,EAAC;wBAAmB;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChC,CAAC,eACFnD,OAAA;0BAAOwC,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAA6B;0BAAAO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNnD,OAAA,CAACP,GAAG;sBAACuE,EAAE,EAAE,CAAE;sBAAAvB,QAAA,eACTzC,OAAA,CAACZ,IAAI,CAACqE,KAAK;wBAACjB,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BzC,OAAA,CAACZ,IAAI,CAACsE,KAAK;0BAAClB,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCzC,OAAA;4BAAGwC,SAAS,EAAC;0BAAqB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,oBACzC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,IAAI,CAACuE,OAAO;0BACXC,IAAI,EAAC,UAAU;0BACfxC,IAAI,EAAC,iBAAiB;0BACtByC,QAAQ;0BACRxC,KAAK,EAAEhB,QAAQ,CAACI,eAAgB;0BAChCqD,QAAQ,EAAE5C,YAAa;0BACvBsB,SAAS,EAAC,YAAY;0BACtBuB,WAAW,EAAC;wBAAuB;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnD,OAAA;kBAAKwC,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnCzC,OAAA;oBAAIwC,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAACzC,OAAA;sBAAGwC,SAAS,EAAC;oBAAsB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,qBAAiB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5FnD,OAAA,CAACR,GAAG;oBAAAiD,QAAA,gBACFzC,OAAA,CAACP,GAAG;sBAACuE,EAAE,EAAE,CAAE;sBAAAvB,QAAA,eACTzC,OAAA,CAACZ,IAAI,CAACqE,KAAK;wBAACjB,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BzC,OAAA,CAACZ,IAAI,CAACsE,KAAK;0BAAClB,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCzC,OAAA;4BAAGwC,SAAS,EAAC;0BAAkB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,gBACtC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,IAAI,CAACuE,OAAO;0BACXC,IAAI,EAAC,MAAM;0BACXxC,IAAI,EAAC,cAAc;0BACnByC,QAAQ;0BACRxC,KAAK,EAAEhB,QAAQ,CAACK,YAAa;0BAC7BoD,QAAQ,EAAE5C,YAAa;0BACvBsB,SAAS,EAAC,YAAY;0BACtBuB,WAAW,EAAC;wBAAyB;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC,eACNnD,OAAA,CAACP,GAAG;sBAACuE,EAAE,EAAE,CAAE;sBAAAvB,QAAA,eACTzC,OAAA,CAACZ,IAAI,CAACqE,KAAK;wBAACjB,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBAC1BzC,OAAA,CAACZ,IAAI,CAACsE,KAAK;0BAAClB,SAAS,EAAC,iBAAiB;0BAAAC,QAAA,gBACrCzC,OAAA;4BAAGwC,SAAS,EAAC;0BAAmB;4BAAAQ,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,iBACvC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAY,CAAC,eACbnD,OAAA,CAACZ,IAAI,CAACuE,OAAO;0BACXC,IAAI,EAAC,KAAK;0BACVxC,IAAI,EAAC,eAAe;0BACpByC,QAAQ;0BACRxC,KAAK,EAAEhB,QAAQ,CAACM,aAAc;0BAC9BmD,QAAQ,EAAE5C,YAAa;0BACvBsB,SAAS,EAAC,YAAY;0BACtBuB,WAAW,EAAC,8BAA8B;0BAC1CE,SAAS,EAAC;wBAAI;0BAAAjB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACf,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACQ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENnD,OAAA;oBAAKwC,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BzC,OAAA;sBAAGwC,SAAS,EAAC;oBAAyB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAC3CnD,OAAA;sBAAAyC,QAAA,EAAQ;oBAAwC;sBAAAO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAAAnD,OAAA;sBAAAgD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,6GAEjE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENnD,OAAA;kBAAKwC,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,eACjCzC,OAAA,CAACZ,IAAI,CAAC8E,KAAK;oBACTN,IAAI,EAAC,UAAU;oBACfO,EAAE,EAAC,gBAAgB;oBACnBC,KAAK,eAAEpE,OAAA;sBAAAyC,QAAA,GAAM,iBAAe,eAAAzC,OAAA,CAACN,IAAI;wBAAC2E,EAAE,EAAC,GAAG;wBAAC7B,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,SAAK,eAAAnD,OAAA,CAACN,IAAI;wBAAC2E,EAAE,EAAC,GAAG;wBAAC7B,SAAS,EAAC,WAAW;wBAAAC,QAAA,EAAC;sBAAc;wBAAAO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAE;oBAC3JX,SAAS,EAAC,eAAe;oBACzBqB,QAAQ;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENnD,OAAA,CAACX,MAAM;kBACLiF,QAAQ,EAAE1D,OAAQ;kBAClB4B,SAAS,EAAC,qBAAqB;kBAC/BoB,IAAI,EAAC,QAAQ;kBAAAnB,QAAA,EAEZ7B,OAAO,gBACNZ,OAAA,CAAAE,SAAA;oBAAAuC,QAAA,gBACEzC,OAAA;sBAAMwC,SAAS,EAAC,uCAAuC;sBAAC+B,IAAI,EAAC,QAAQ;sBAAC,eAAY;oBAAM;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,uBAElG;kBAAA,eAAE,CAAC,gBAEHnD,OAAA,CAAAE,SAAA;oBAAAuC,QAAA,GAAE,iBAAe,eAAAzC,OAAA;sBAAGwC,SAAS,EAAC;oBAAyB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA,eAAE;gBAC/D;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEPnD,OAAA;gBAAKwC,SAAS,EAAC,aAAa;gBAAAC,QAAA,GAAC,2BACF,eAAAzC,OAAA,CAACN,IAAI;kBAAC2E,EAAE,EAAC,QAAQ;kBAAC7B,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAO;kBAAAO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV;AAAC/C,EAAA,CArOuBD,QAAQ;EAAA,QASTP,OAAO,EACOC,QAAQ,EAC1BF,WAAW;AAAA;AAAA6E,EAAA,GAXNrE,QAAQ;AAAA,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}