import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import jwt_decode from 'jwt-decode';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';

const AuthContext = createContext();

export function AuthProvider({ children }) {
  const [currentUser, setCurrentUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const token = localStorage.getItem('token');
    const storedUserData = localStorage.getItem('userData');

    console.log('Initial load - Token exists:', !!token);
    console.log('Initial load - Stored user data exists:', !!storedUserData);

    if (token) {
      try {
        // Check if token is expired
        const decoded = jwt_decode(token);
        const currentTime = Date.now() / 1000;

        if (decoded.exp && decoded.exp < currentTime) {
          // Token is expired
          console.log('Token expired, logging out');
          logout();
        } else {
          // Try to use stored user data first for more complete information
          let userData;

          if (storedUserData) {
            try {
              userData = JSON.parse(storedUserData);
              console.log('Using stored user data:', userData);
            } catch (e) {
              console.error('Error parsing stored user data:', e);
            }
          }

          // If no stored data or parsing failed, use decoded token
          if (!userData) {
            userData = {
              ...decoded,
              token: token,
              company_name: decoded.company_name || '',
              username: decoded.sub || '',
              email: decoded.email || ''
            };
            console.log('Using decoded token data:', userData);
          }

          // Set currentUser with all available data
          setCurrentUser(userData);
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        }
      } catch (error) {
        console.error('Invalid token:', error);
        logout();
      }
    }
    setLoading(false);
  }, []);

  const login = async (username, password) => {
    try {
      // Create form data instead of JSON
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('password', password);

      console.log('Attempting login for:', username);

      const response = await axios.post(`${API_BASE_URL}/token`, formData, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      console.log('Login response:', response.data);

      const token = response.data.access_token;
      localStorage.setItem('token', token);

      // Decode the JWT token
      const decoded = jwt_decode(token);
      console.log('Decoded token:', decoded);

      // Get user info from response if available
      const userInfo = response.data.user_info || {};
      console.log('User info from response:', userInfo);

      // Set currentUser to include both decoded data, token, and user info
      const userData = {
        ...decoded,
        token: token,  // Add the token to the user object
        // Prioritize user_info from response, then fall back to decoded token data
        company_name: userInfo.company_name || decoded.company_name || '',
        username: userInfo.username || decoded.sub || username,
        email: userInfo.email || decoded.email || ''
      };

      console.log('Setting current user data:', userData);
      setCurrentUser(userData);

      // Store the complete user data in localStorage for persistence
      localStorage.setItem('userData', JSON.stringify(userData));

      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      navigate('/dashboard');
      return true;
    } catch (error) {
      console.error('Login error:', error.response?.data || error.message);
      throw error;
    }
  };

  const register = async (userData) => {
    try {
      console.log('Registering new user:', userData.email);
      console.log('Company name being registered:', userData.company_name);

      const response = await axios.post(`${API_BASE_URL}/register`, userData);
      console.log('Registration successful, response:', response.data);

      // Only attempt login if registration was successful
      console.log('Attempting login after registration');
      return await login(userData.email, userData.password); // Use email as username
    } catch (error) {
      console.error('Registration error:', error.response?.data || error.message);
      throw error;
    }
  };

  const logout = () => {
    console.log('Logging out, clearing user data');
    localStorage.removeItem('token');
    localStorage.removeItem('userData');
    setCurrentUser(null);
    delete axios.defaults.headers.common['Authorization'];
    navigate('/login');
  };

  const value = {
    currentUser,
    login,
    register,
    logout,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  return useContext(AuthContext);
}
