#!/usr/bin/env python3
"""
Migration script to add profile completion fields to existing users
"""

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from database import SQLALCHEMY_DATABASE_URL, SessionLocal
from models import User
import sys

def migrate_profile_completion():
    """Add profile completion fields to existing users"""
    
    # Create session
    db = SessionLocal()
    
    try:
        # Check if columns exist, if not add them
        print("Checking if profile completion columns exist...")
        
        # Add profile_completed column if it doesn't exist
        try:
            db.execute(text("ALTER TABLE users ADD COLUMN profile_completed BOOLEAN DEFAULT FALSE"))
            print("Added profile_completed column")
        except Exception as e:
            if "Duplicate column name" in str(e) or "already exists" in str(e):
                print("profile_completed column already exists")
            else:
                print(f"Error adding profile_completed column: {e}")
        
        # Add profile_completion_percentage column if it doesn't exist
        try:
            db.execute(text("ALTER TABLE users ADD COLUMN profile_completion_percentage INTEGER DEFAULT 0"))
            print("Added profile_completion_percentage column")
        except Exception as e:
            if "Duplicate column name" in str(e) or "already exists" in str(e):
                print("profile_completion_percentage column already exists")
            else:
                print(f"Error adding profile_completion_percentage column: {e}")
        
        db.commit()
        
        # Update existing users with profile completion data
        print("Updating existing users with profile completion data...")
        
        users = db.query(User).all()
        updated_count = 0
        
        for user in users:
            # Calculate profile completion for existing users
            completion_percentage = user.calculate_profile_completion()
            profile_completed = user.is_profile_complete_for_invoice()
            
            # Update the user
            user.profile_completion_percentage = completion_percentage
            user.profile_completed = profile_completed
            
            updated_count += 1
            print(f"Updated user {user.email}: {completion_percentage}% complete, can create invoice: {profile_completed}")
        
        db.commit()
        print(f"Successfully updated {updated_count} users")
        
    except Exception as e:
        print(f"Migration failed: {e}")
        db.rollback()
        sys.exit(1)
    finally:
        db.close()

if __name__ == "__main__":
    print("Starting profile completion migration...")
    migrate_profile_completion()
    print("Migration completed successfully!")
